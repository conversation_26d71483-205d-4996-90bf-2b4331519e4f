{"logs": [{"outputFile": "com.example.todolist.app-mergeDebugResources-36:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.9\\transforms\\ccc6704ec40174d3a5896d53e2b7c03a\\transformed\\browser-1.4.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "5945,6218,6310,6411", "endColumns": "82,91,100,92", "endOffsets": "6023,6305,6406,6499"}}, {"source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.9\\transforms\\edf9bf37f6d93d447fb63ea57318ab6f\\transformed\\play-services-basement-18.2.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "4993", "endColumns": "98", "endOffsets": "5087"}}, {"source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.9\\transforms\\1351b8c1d00e574e81c77e445d78a1cb\\transformed\\play-services-base-18.0.1\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4131,4232,4361,4476,4578,4683,4799,4901,5092,5200,5301,5431,5546,5650,5758,5814,5871", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "4227,4356,4471,4573,4678,4794,4896,4988,5195,5296,5426,5541,5645,5753,5809,5866,5940"}}, {"source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.9\\transforms\\47acb2d51539ebc7e1b6659adb48b4bc\\transformed\\appcompat-1.7.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,487,587,669,766,872,949,1024,1115,1208,1305,1401,1495,1588,1683,1775,1866,1957,2035,2131,2226,2321,2418,2514,2612,2760,10725", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "387,482,582,664,761,867,944,1019,1110,1203,1300,1396,1490,1583,1678,1770,1861,1952,2030,2126,2221,2316,2413,2509,2607,2755,2849,10799"}}, {"source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.9\\transforms\\d8ec8ac963a493a193201cc8ee075ff5\\transformed\\material-1.12.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,911,973,1050,1109,1168,1246,1307,1364,1420,1479,1537,1591,1676,1732,1790,1844,1909,2001,2075,2147,2226,2300,2376,2498,2560,2622,2721,2800,2874,2924,2975,3041,3105,3174,3245,3316,3377,3448,3515,3575,3661,3740,3807,3890,3975,4049,4114,4190,4238,4311,4375,4451,4529,4591,4655,4718,4783,4863,4939,5017,5093,5147,5202,5271,5346,5419", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "242,306,368,438,508,585,676,782,855,906,968,1045,1104,1163,1241,1302,1359,1415,1474,1532,1586,1671,1727,1785,1839,1904,1996,2070,2142,2221,2295,2371,2493,2555,2617,2716,2795,2869,2919,2970,3036,3100,3169,3240,3311,3372,3443,3510,3570,3656,3735,3802,3885,3970,4044,4109,4185,4233,4306,4370,4446,4524,4586,4650,4713,4778,4858,4934,5012,5088,5142,5197,5266,5341,5414,5484"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2854,2918,2980,3050,3120,3861,3952,4058,6028,6079,6141,6504,6563,6622,6700,6761,6818,6874,6933,6991,7045,7130,7186,7244,7298,7363,7455,7529,7601,7680,7754,7830,7952,8014,8076,8175,8254,8328,8378,8429,8495,8559,8628,8699,8770,8831,8902,8969,9029,9115,9194,9261,9344,9429,9503,9568,9644,9692,9765,9829,9905,9983,10045,10109,10172,10237,10317,10393,10471,10547,10601,10656,10804,10879,10952", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,135,136,137", "endColumns": "12,63,61,69,69,76,90,105,72,50,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,71,78,73,75,121,61,61,98,78,73,49,50,65,63,68,70,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68,74,72,69", "endOffsets": "292,2913,2975,3045,3115,3192,3947,4053,4126,6074,6136,6213,6558,6617,6695,6756,6813,6869,6928,6986,7040,7125,7181,7239,7293,7358,7450,7524,7596,7675,7749,7825,7947,8009,8071,8170,8249,8323,8373,8424,8490,8554,8623,8694,8765,8826,8897,8964,9024,9110,9189,9256,9339,9424,9498,9563,9639,9687,9760,9824,9900,9978,10040,10104,10167,10232,10312,10388,10466,10542,10596,10651,10720,10874,10947,11017"}}, {"source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.9\\transforms\\94b25b2e0232f11eacc589061e7c8810\\transformed\\core-1.13.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "38,39,40,41,42,43,44,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3197,3289,3390,3484,3578,3671,3765,11022", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "3284,3385,3479,3573,3666,3760,3856,11118"}}]}]}