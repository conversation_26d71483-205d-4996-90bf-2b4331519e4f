[{"merged": "com.example.todolist.app-debug-31:/drawable_edit_text_background.xml.flat", "source": "com.example.todolist.app-main-33:/drawable/edit_text_background.xml"}, {"merged": "com.example.todolist.app-debug-31:/layout_activity_main.xml.flat", "source": "com.example.todolist.app-main-33:/layout/activity_main.xml"}, {"merged": "com.example.todolist.app-debug-31:/layout_dialog_add_task.xml.flat", "source": "com.example.todolist.app-main-33:/layout/dialog_add_task.xml"}, {"merged": "com.example.todolist.app-debug-31:/drawable-mdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-27:/drawable-mdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-31:/drawable-hdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-27:/drawable-hdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.example.todolist.app-main-33:/mipmap-anydpi/ic_launcher.xml"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-31:/drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "com.example.todolist.app-pngs-27:/drawable-anydpi-v24/ic_launcher_foreground.xml"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-31:/drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-27:/drawable-xhdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-31:/drawable_ic_launcher_background.xml.flat", "source": "com.example.todolist.app-main-33:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.todolist.app-debug-31:/drawable_status_background.xml.flat", "source": "com.example.todolist.app-main-33:/drawable/status_background.xml"}, {"merged": "com.example.todolist.app-debug-31:/menu_main_menu.xml.flat", "source": "com.example.todolist.app-main-33:/menu/main_menu.xml"}, {"merged": "com.example.todolist.app-debug-31:/drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-27:/drawable-xxxhdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-31:/layout_dialog_filter.xml.flat", "source": "com.example.todolist.app-main-33:/layout/dialog_filter.xml"}, {"merged": "com.example.todolist.app-debug-31:/drawable-ldpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-27:/drawable-ldpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.example.todolist.app-main-33:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-31:/drawable_ic_calendar.xml.flat", "source": "com.example.todolist.app-main-33:/drawable/ic_calendar.xml"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-31:/xml_backup_rules.xml.flat", "source": "com.example.todolist.app-main-33:/xml/backup_rules.xml"}, {"merged": "com.example.todolist.app-debug-31:/xml_data_extraction_rules.xml.flat", "source": "com.example.todolist.app-main-33:/xml/data_extraction_rules.xml"}, {"merged": "com.example.todolist.app-debug-31:/drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-27:/drawable-xxhdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-31:/layout_task_item.xml.flat", "source": "com.example.todolist.app-main-33:/layout/task_item.xml"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-31:/drawable_task_item_background.xml.flat", "source": "com.example.todolist.app-main-33:/drawable/task_item_background.xml"}, {"merged": "com.example.todolist.app-debug-31:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-33:/mipmap-mdpi/ic_launcher_round.webp"}]