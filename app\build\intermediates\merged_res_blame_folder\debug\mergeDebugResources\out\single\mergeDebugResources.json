[{"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-mdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-mdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\anim_slide_out_left.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\anim\\slide_out_left.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_priority_background.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\priority_background.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_empty_state.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\empty_state.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_ic_calendar.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\ic_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\anim_slide_in_right.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\anim\\slide_in_right.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_ic_statistics.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\ic_statistics.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_activity_statistics.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\activity_statistics.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-xxxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_ic_add.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\ic_add.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "com.example.todolist.app-debug-32:/layout_activity_main.xml.flat", "source": "com.example.todolist.app-main-34:/layout/activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_ic_empty_tasks.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\ic_empty_tasks.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\status_background.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_edit_text_background.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_dialog_filter.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\dialog_filter.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-xhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-ldpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-ldpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_task_item_background.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\task_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_task_item.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\task_item.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-xxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_dialog_add_task.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\dialog_add_task.xml"}, {"merged": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\anim_scale_in.xml.flat", "source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\anim\\scale_in.xml"}]