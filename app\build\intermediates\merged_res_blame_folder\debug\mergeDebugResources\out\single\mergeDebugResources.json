[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-hdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-hdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-mdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-mdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_dialog_add_task.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\dialog_add_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_dialog_filter.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\dialog_filter.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_task_item_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\task_item_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_ic_calendar.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\ic_calendar.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\layout_task_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\layout\\task_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-32:/layout_dialog_add_task.xml.flat", "source": "com.example.todolist.app-main-34:/layout/dialog_add_task.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-ldpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-ldpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-xhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-anydpi_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-anydpi\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-xxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-anydpi_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-anydpi\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-pngs-28:\\drawable-xxxhdpi\\ic_launcher_foreground.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-32:/layout_dialog_filter.xml.flat", "source": "com.example.todolist.app-main-34:/layout/dialog_filter.xml"}, {"merged": "com.example.todolist.app-debug-32:/drawable_edit_text_background.xml.flat", "source": "com.example.todolist.app-main-34:/drawable/edit_text_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-debug-32:\\drawable_status_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.9\\com.example.todolist.app-main-34:\\drawable\\status_background.xml"}]