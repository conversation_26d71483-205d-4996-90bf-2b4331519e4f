[{"merged": "com.example.todolist.app-debug-38:/layout_activity_login.xml.flat", "source": "com.example.todolist.app-main-40:/layout/activity_login.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_task_item_background.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/task_item_background.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-38:/drawable-mdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-33:/drawable-mdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-38:/layout_empty_state.xml.flat", "source": "com.example.todolist.app-main-40:/layout/empty_state.xml"}, {"merged": "com.example.todolist.app-debug-38:/anim_fade_in.xml.flat", "source": "com.example.todolist.app-main-40:/anim/fade_in.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-38:/drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "com.example.todolist.app-pngs-33:/drawable-anydpi-v24/ic_launcher_foreground.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_sort.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_sort.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_edit_text_background.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/edit_text_background.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-33:/drawable-xxhdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_empty_tasks.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_empty_tasks.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-38:/layout_activity_statistics.xml.flat", "source": "com.example.todolist.app-main-40:/layout/activity_statistics.xml"}, {"merged": "com.example.todolist.app-debug-38:/menu_main_menu.xml.flat", "source": "com.example.todolist.app-main-40:/menu/main_menu.xml"}, {"merged": "com.example.todolist.app-debug-38:/anim_slide_out_left.xml.flat", "source": "com.example.todolist.app-main-40:/anim/slide_out_left.xml"}, {"merged": "com.example.todolist.app-debug-38:/layout_dialog_add_task.xml.flat", "source": "com.example.todolist.app-main-40:/layout/dialog_add_task.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-38:/layout_dialog_filter.xml.flat", "source": "com.example.todolist.app-main-40:/layout/dialog_filter.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-38:/drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-33:/drawable-xhdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-38:/drawable-hdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-33:/drawable-hdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-38:/layout_task_item.xml.flat", "source": "com.example.todolist.app-main-40:/layout/task_item.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_calendar.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_calendar.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_add.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_add.xml"}, {"merged": "com.example.todolist.app-debug-38:/anim_slide_in_right.xml.flat", "source": "com.example.todolist.app-main-40:/anim/slide_in_right.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-38:/drawable_fab_background.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/fab_background.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_statistics.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_statistics.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_modern_card_background.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/modern_card_background.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_google.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_google.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_status_background.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/status_background.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-38:/drawable-ldpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-33:/drawable-ldpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-38:/anim_scale_in.xml.flat", "source": "com.example.todolist.app-main-40:/anim/scale_in.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "com.example.todolist.app-pngs-33:/drawable-xxxhdpi/ic_launcher_foreground.png"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_app_logo.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_app_logo.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_google_sign_in_button.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/google_sign_in_button.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_gradient_primary.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/gradient_primary.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_notification.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_notification.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_account.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_account.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_priority_background.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/priority_background.xml"}, {"merged": "com.example.todolist.app-debug-38:/layout_dialog_sort.xml.flat", "source": "com.example.todolist.app-main-40:/layout/dialog_sort.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.example.todolist.app-debug-38:/font-v26_inter_bold.xml.flat", "source": "com.example.todolist.app-main-40:/font-v26/inter_bold.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_gradient_accent.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/gradient_accent.xml"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_filter.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_filter.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-38:/drawable_ic_launcher_background.xml.flat", "source": "com.example.todolist.app-main-40:/drawable/ic_launcher_background.xml"}, {"merged": "com.example.todolist.app-debug-38:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.example.todolist.app-main-40:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.example.todolist.app-debug-38:/layout_activity_main.xml.flat", "source": "com.example.todolist.app-main-40:/layout/activity_main.xml"}]