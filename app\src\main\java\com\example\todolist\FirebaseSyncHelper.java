package com.example.todolist;

import android.content.Context;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.OnFailureListener;
import com.google.android.gms.tasks.OnSuccessListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.firestore.DocumentSnapshot;
import com.google.firebase.firestore.FirebaseFirestore;
import com.google.firebase.firestore.QueryDocumentSnapshot;
import com.google.firebase.firestore.QuerySnapshot;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class FirebaseSyncHelper {
    
    private static final String COLLECTION_TASKS = "user_tasks";
    private static final String FIELD_TASKS_DATA = "tasks";
    private static final String FIELD_LAST_UPDATED = "lastUpdated";
    
    private FirebaseFirestore db;
    private Context context;
    private SyncListener syncListener;
    
    public interface SyncListener {
        void onSyncSuccess(ArrayList<com.example.todolist.Task> tasks);
        void onSyncFailure(String error);
        void onUploadSuccess();
        void onUploadFailure(String error);
    }
    
    public FirebaseSyncHelper(Context context, SyncListener listener) {
        this.context = context;
        this.syncListener = listener;
        this.db = FirebaseFirestore.getInstance();
    }
    
    public void uploadTasks(String userId, ArrayList<com.example.todolist.Task> tasks) {
        if (userId == null || userId.isEmpty()) {
            if (syncListener != null) {
                syncListener.onUploadFailure("İstifadəçi ID-si yoxdur");
            }
            return;
        }
        
        // Convert tasks to JSON
        Gson gson = new Gson();
        String tasksJson = gson.toJson(tasks);
        
        // Create document data
        Map<String, Object> data = new HashMap<>();
        data.put(FIELD_TASKS_DATA, tasksJson);
        data.put(FIELD_LAST_UPDATED, System.currentTimeMillis());
        
        // Upload to Firestore
        db.collection(COLLECTION_TASKS)
                .document(userId)
                .set(data)
                .addOnSuccessListener(new OnSuccessListener<Void>() {
                    @Override
                    public void onSuccess(Void aVoid) {
                        if (syncListener != null) {
                            syncListener.onUploadSuccess();
                        }
                        Toast.makeText(context, "Məlumatlar cloud-a yükləndi", Toast.LENGTH_SHORT).show();
                    }
                })
                .addOnFailureListener(new OnFailureListener() {
                    @Override
                    public void onFailure(@NonNull Exception e) {
                        if (syncListener != null) {
                            syncListener.onUploadFailure("Yükləmə xətası: " + e.getMessage());
                        }
                    }
                });
    }
    
    public void downloadTasks(String userId) {
        if (userId == null || userId.isEmpty()) {
            if (syncListener != null) {
                syncListener.onSyncFailure("İstifadəçi ID-si yoxdur");
            }
            return;
        }
        
        db.collection(COLLECTION_TASKS)
                .document(userId)
                .get()
                .addOnCompleteListener(new OnCompleteListener<DocumentSnapshot>() {
                    @Override
                    public void onComplete(@NonNull Task<DocumentSnapshot> task) {
                        if (task.isSuccessful()) {
                            DocumentSnapshot document = task.getResult();
                            if (document.exists()) {
                                String tasksJson = document.getString(FIELD_TASKS_DATA);
                                if (tasksJson != null && !tasksJson.isEmpty()) {
                                    try {
                                        Gson gson = new Gson();
                                        Type type = new TypeToken<ArrayList<com.example.todolist.Task>>() {}.getType();
                                        ArrayList<com.example.todolist.Task> tasks = gson.fromJson(tasksJson, type);
                                        
                                        if (tasks != null) {
                                            if (syncListener != null) {
                                                syncListener.onSyncSuccess(tasks);
                                            }
                                            Toast.makeText(context, tasks.size() + " tapşırıq yükləndi", Toast.LENGTH_SHORT).show();
                                        } else {
                                            if (syncListener != null) {
                                                syncListener.onSyncSuccess(new ArrayList<>());
                                            }
                                        }
                                    } catch (Exception e) {
                                        if (syncListener != null) {
                                            syncListener.onSyncFailure("Məlumat parse xətası: " + e.getMessage());
                                        }
                                    }
                                } else {
                                    if (syncListener != null) {
                                        syncListener.onSyncSuccess(new ArrayList<>());
                                    }
                                    Toast.makeText(context, "Cloud-da məlumat yoxdur", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                if (syncListener != null) {
                                    syncListener.onSyncSuccess(new ArrayList<>());
                                }
                                Toast.makeText(context, "Cloud-da məlumat yoxdur", Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            if (syncListener != null) {
                                syncListener.onSyncFailure("Yükləmə xətası: " + task.getException().getMessage());
                            }
                        }
                    }
                });
    }
    
    public void syncTasks(String userId, ArrayList<com.example.todolist.Task> localTasks) {
        // First download cloud tasks, then merge with local tasks
        downloadTasks(userId);
    }
    
    public void deleteUserData(String userId) {
        if (userId == null || userId.isEmpty()) {
            return;
        }
        
        db.collection(COLLECTION_TASKS)
                .document(userId)
                .delete()
                .addOnSuccessListener(new OnSuccessListener<Void>() {
                    @Override
                    public void onSuccess(Void aVoid) {
                        Toast.makeText(context, "Cloud məlumatları silindi", Toast.LENGTH_SHORT).show();
                    }
                })
                .addOnFailureListener(new OnFailureListener() {
                    @Override
                    public void onFailure(@NonNull Exception e) {
                        Toast.makeText(context, "Silmə xətası: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    }
                });
    }
}
