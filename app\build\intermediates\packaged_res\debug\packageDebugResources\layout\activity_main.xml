<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/neutral_100"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- Header Section -->
        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/primary_600"
            android:elevation="0dp"
            app:elevation="0dp"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

            <androidx.appcompat.widget.Toolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:background="@drawable/gradient_primary"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/app_name"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@color/neutral_0"
                        android:fontFamily="@font/inter_bold"
                        android:letterSpacing="0.02" />

                    <ImageButton
                        android:id="@+id/filterButton"
                        android:layout_width="48dp"
                        android:layout_height="48dp"
                        android:src="@drawable/ic_filter"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:tint="@color/neutral_0"
                        android:contentDescription="@string/filter"
                        android:layout_marginEnd="8dp" />

                </LinearLayout>

            </androidx.appcompat.widget.Toolbar>

        </com.google.android.material.appbar.AppBarLayout>

        <!-- Tasks List Section -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingHorizontal="8dp"
            android:paddingTop="8dp">

            <TextView
                android:id="@+id/tasksCountTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/total_tasks"
                android:textSize="14sp"
                android:textColor="@color/neutral_700"
                android:layout_marginBottom="8dp"
                android:paddingHorizontal="8dp"
                android:fontFamily="@font/inter_medium" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipeRefreshLayout"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <ListView
                        android:id="@+id/tasksListView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:divider="@android:color/transparent"
                        android:dividerHeight="8dp"
                        android:scrollbars="vertical"
                        android:clipToPadding="false"
                        android:paddingBottom="80dp"
                        android:background="@color/neutral_100" />

                </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

                <include
                    android:id="@+id/emptyStateView"
                    layout="@layout/empty_state"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone" />

            </FrameLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- Floating Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddTask"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="24dp"
        android:src="@drawable/ic_add"
        android:contentDescription="@string/add_task_button"
        app:backgroundTint="@color/primary_500"
        app:tint="@color/neutral_0"
        app:elevation="8dp"
        app:fabSize="normal"
        app:rippleColor="@color/primary_200" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
