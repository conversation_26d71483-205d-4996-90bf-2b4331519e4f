<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- App theme colors -->
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark_color">#1976D2</color>
    <color name="accent_color">#4CAF50</color>
    <color name="secondary_color">#FF9800</color>

    <!-- Status colors -->
    <color name="status_pending">#FF9800</color>
    <color name="status_in_progress">#2196F3</color>
    <color name="status_completed">#4CAF50</color>

    <!-- Background colors -->
    <color name="background_pending">#FFF3E0</color>
    <color name="background_in_progress">#E3F2FD</color>
    <color name="background_completed">#E8F5E8</color>

    <!-- Text colors -->
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="text_hint">#BDBDBD</color>

    <!-- Surface colors for light mode -->
    <color name="surface_color">#FFFFFF</color>
    <color name="card_background">#FFFFFF</color>
    <color name="divider_color">#E0E0E0</color>
</resources>