<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- App theme colors -->
    <color name="primary_color">#6200EE</color>
    <color name="primary_dark_color">#3700B3</color>
    <color name="accent_color">#03DAC6</color>
    <color name="secondary_color">#018786</color>

    <!-- Status colors -->
    <color name="status_pending">#FF9800</color>
    <color name="status_in_progress">#2196F3</color>
    <color name="status_completed">#4CAF50</color>

    <!-- Background colors -->
    <color name="background_pending">#FFF8E1</color>
    <color name="background_in_progress">#E3F2FD</color>
    <color name="background_completed">#E8F5E8</color>

    <!-- Text colors -->
    <color name="text_primary">#1C1C1C</color>
    <color name="text_secondary">#666666</color>
    <color name="text_hint">#999999</color>

    <!-- Surface colors for light mode -->
    <color name="surface_color">#FAFAFA</color>
    <color name="card_background">#FFFFFF</color>
    <color name="divider_color">#E0E0E0</color>

    <!-- Additional light mode colors -->
    <color name="error_color">#B00020</color>
    <color name="success_color">#00C853</color>
    <color name="warning_color">#FF8F00</color>
</resources>