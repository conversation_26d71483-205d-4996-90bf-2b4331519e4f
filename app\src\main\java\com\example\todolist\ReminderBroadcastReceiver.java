package com.example.todolist;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

public class ReminderBroadcastReceiver extends BroadcastReceiver {

    @Override
    public void onReceive(Context context, Intent intent) {
        String title = intent.getStringExtra(NotificationHelper.EXTRA_TASK_TITLE);
        String description = intent.getStringExtra(NotificationHelper.EXTRA_TASK_DESCRIPTION);
        int notificationId = intent.getIntExtra(NotificationHelper.EXTRA_NOTIFICATION_ID, 0);

        if (title != null && description != null) {
            NotificationHelper.showTaskReminderNotification(context, title, description, notificationId);
            Log.d("ReminderBroadcast", "Received reminder for: " + title);
        } else {
            Log.e("ReminderBroadcast", "Received reminder with missing title or description.");
        }
    }
}
