-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:2:1-43:12
INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:2:1-43:12
INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:2:1-43:12
INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:2:1-43:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d8ec8ac963a493a193201cc8ee075ff5\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\0c850af394c9f19314b841fcb168ef45\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5d34a3e3ab9248f2d7a5fcbd72642d45\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\47acb2d51539ebc7e1b6659adb48b4bc\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:17:1-75:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9646aad0dd6e4672fd7ce77e800ba944\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\6ffe360b3274e8d4ef9d08edaedaa91d\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7b1eee99041bf1d3f7c0edb72c8b39c2\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7d4f9f1b83ca05a1abc7447df145ded3\transformed\play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1853e088a7409118c7d3bb99ffd6dda9\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\55c5b4bff02d167d3cfa132415f7c1bd\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aee747f39a8ca605c682324aa79b0c6\transformed\recaptcha-18.1.2\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bffa301f6ebb411c65e485a5b8f52b65\transformed\integrity-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7db233d256b4176616915a6e20e95627\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b52ed8f98cafa40fd271bfeff22227aa\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\cdd562ae3d28555dc6385964e68e86da\transformed\core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2342a6240f92a130700ffa9393d16894\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\98b61775cad0d12ad38905c0b2409c01\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7ce5cf13d729ed5aad66ee3a230fd592\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b840c59d10651504a233ff733d9b47af\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aa7f03813fe1b763b7122eee844f320\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\8c00d024496eabb74c56b966f2cbf6ee\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\658c4b6697825d9d008e1a42f53c7a1e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\69b5c0dc6c025feafe7eb1ef90494c3c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f76e7c9b192a20648e3a812502851abf\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ccc6704ec40174d3a5896d53e2b7c03a\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a60ea27909bce39452ab577979cefc71\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\41c3cd471c2d638d7f39c693813ff492\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\06f8a9f26e3222e8ba9848ca28538963\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ffbe0b8035152d3bc28eee4116464488\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7fe705e45fb23f665c7eb8611efc4f85\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1de238fcc0a42c2b587ebee7085bfcbd\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\4f7064da9546fca51e7936f39df6eaeb\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c3492ee4dcea561ab54ec13c1abcc2d7\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\cf75aa9b18f06d3b2335f904dba4e321\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9db969efd78c626d748162acb38ce95f\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f54c7c84c5f67e8ee6537041e7fd1bab\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f921a27828947145d90f6239f7938681\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\e4dd37a652b916738c3c0bd41b505fa5\transformed\grpc-android-1.52.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2a78d84440dd84ac3796151e98073ac9\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\43ed0d9722d084c7cfe8607187d06b67\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\788f745b4f6aaef48235a81abb54c6f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\456115ac9643a36b86fb7debc84af7cb\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\305b1056f1eb0661a27cdbb3a0ef0b11\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\6d92b110c97dce035831179f64032edc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\136186f76116a76577e933705066fb60\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\fd3c4a0a770afa92d3461d489257f995\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b70b3d42a727a5a5f87762f38ae210e4\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f72fe394f3b44b547edef032fe49296e\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\da7c2a225da80a1abbfe7639951eba2c\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\8683e8e523293e238091f4971c33510d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b5b44c1112c505878aa38e2feda16718\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:5:5-77
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:5:22-74
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:6:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aee747f39a8ca605c682324aa79b0c6\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aee747f39a8ca605c682324aa79b0c6\transformed\recaptcha-18.1.2\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aee747f39a8ca605c682324aa79b0c6\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aee747f39a8ca605c682324aa79b0c6\transformed\recaptcha-18.1.2\AndroidManifest.xml:8:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\e4dd37a652b916738c3c0bd41b505fa5\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\e4dd37a652b916738c3c0bd41b505fa5\transformed\grpc-android-1.52.1\AndroidManifest.xml:9:5-79
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:8:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:9:5-81
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:9:22-78
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:10:5-111
	tools:ignore
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:10:80-108
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:10:22-79
application
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:12:5-41:19
INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:12:5-41:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d8ec8ac963a493a193201cc8ee075ff5\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d8ec8ac963a493a193201cc8ee075ff5\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\0c850af394c9f19314b841fcb168ef45\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\0c850af394c9f19314b841fcb168ef45\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7d4f9f1b83ca05a1abc7447df145ded3\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7d4f9f1b83ca05a1abc7447df145ded3\transformed\play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1853e088a7409118c7d3bb99ffd6dda9\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1853e088a7409118c7d3bb99ffd6dda9\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bffa301f6ebb411c65e485a5b8f52b65\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bffa301f6ebb411c65e485a5b8f52b65\transformed\integrity-1.1.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7db233d256b4176616915a6e20e95627\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7db233d256b4176616915a6e20e95627\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9db969efd78c626d748162acb38ce95f\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9db969efd78c626d748162acb38ce95f\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\788f745b4f6aaef48235a81abb54c6f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\788f745b4f6aaef48235a81abb54c6f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\fd3c4a0a770afa92d3461d489257f995\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\fd3c4a0a770afa92d3461d489257f995\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:16:9-35
	android:label
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:15:9-41
	android:icon
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:13:9-35
	android:theme
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:17:9-46
activity#com.example.todolist.LoginActivity
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:18:9-26:20
	android:exported
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:20:13-36
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:19:13-42
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:21:13-25:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:22:17-69
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:22:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:24:17-77
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:24:27-74
activity#com.example.todolist.MainActivity
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:28:9-30:40
	android:exported
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:30:13-37
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:29:13-41
activity#com.example.todolist.StatisticsActivity
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:32:9-35:58
	android:parentActivityName
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:35:13-55
	android:exported
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:34:13-37
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:33:13-47
receiver#com.example.todolist.ReminderBroadcastReceiver
ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:37:9-40:40
	android:enabled
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:39:13-35
	android:exported
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:40:13-37
	android:name
		ADDED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:38:13-54
uses-sdk
INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d8ec8ac963a493a193201cc8ee075ff5\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d8ec8ac963a493a193201cc8ee075ff5\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\0c850af394c9f19314b841fcb168ef45\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\0c850af394c9f19314b841fcb168ef45\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5d34a3e3ab9248f2d7a5fcbd72642d45\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5d34a3e3ab9248f2d7a5fcbd72642d45\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\47acb2d51539ebc7e1b6659adb48b4bc\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\47acb2d51539ebc7e1b6659adb48b4bc\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:21:5-23:64
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9646aad0dd6e4672fd7ce77e800ba944\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9646aad0dd6e4672fd7ce77e800ba944\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\6ffe360b3274e8d4ef9d08edaedaa91d\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\6ffe360b3274e8d4ef9d08edaedaa91d\transformed\play-services-auth-api-phone-18.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7b1eee99041bf1d3f7c0edb72c8b39c2\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.4] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7b1eee99041bf1d3f7c0edb72c8b39c2\transformed\play-services-auth-base-18.0.4\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7d4f9f1b83ca05a1abc7447df145ded3\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7d4f9f1b83ca05a1abc7447df145ded3\transformed\play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1853e088a7409118c7d3bb99ffd6dda9\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1853e088a7409118c7d3bb99ffd6dda9\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\55c5b4bff02d167d3cfa132415f7c1bd\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\55c5b4bff02d167d3cfa132415f7c1bd\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aee747f39a8ca605c682324aa79b0c6\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.1.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aee747f39a8ca605c682324aa79b0c6\transformed\recaptcha-18.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bffa301f6ebb411c65e485a5b8f52b65\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bffa301f6ebb411c65e485a5b8f52b65\transformed\integrity-1.1.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7db233d256b4176616915a6e20e95627\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7db233d256b4176616915a6e20e95627\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b52ed8f98cafa40fd271bfeff22227aa\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b52ed8f98cafa40fd271bfeff22227aa\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\cdd562ae3d28555dc6385964e68e86da\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\cdd562ae3d28555dc6385964e68e86da\transformed\core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2342a6240f92a130700ffa9393d16894\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2342a6240f92a130700ffa9393d16894\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\98b61775cad0d12ad38905c0b2409c01\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\98b61775cad0d12ad38905c0b2409c01\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7ce5cf13d729ed5aad66ee3a230fd592\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7ce5cf13d729ed5aad66ee3a230fd592\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b840c59d10651504a233ff733d9b47af\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b840c59d10651504a233ff733d9b47af\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aa7f03813fe1b763b7122eee844f320\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5aa7f03813fe1b763b7122eee844f320\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\8c00d024496eabb74c56b966f2cbf6ee\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\8c00d024496eabb74c56b966f2cbf6ee\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\658c4b6697825d9d008e1a42f53c7a1e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\658c4b6697825d9d008e1a42f53c7a1e\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\69b5c0dc6c025feafe7eb1ef90494c3c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\69b5c0dc6c025feafe7eb1ef90494c3c\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f76e7c9b192a20648e3a812502851abf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f76e7c9b192a20648e3a812502851abf\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ccc6704ec40174d3a5896d53e2b7c03a\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ccc6704ec40174d3a5896d53e2b7c03a\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a60ea27909bce39452ab577979cefc71\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a60ea27909bce39452ab577979cefc71\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\41c3cd471c2d638d7f39c693813ff492\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\41c3cd471c2d638d7f39c693813ff492\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\06f8a9f26e3222e8ba9848ca28538963\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\06f8a9f26e3222e8ba9848ca28538963\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ffbe0b8035152d3bc28eee4116464488\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ffbe0b8035152d3bc28eee4116464488\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7fe705e45fb23f665c7eb8611efc4f85\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7fe705e45fb23f665c7eb8611efc4f85\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1de238fcc0a42c2b587ebee7085bfcbd\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1de238fcc0a42c2b587ebee7085bfcbd\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\4f7064da9546fca51e7936f39df6eaeb\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\4f7064da9546fca51e7936f39df6eaeb\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c3492ee4dcea561ab54ec13c1abcc2d7\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c3492ee4dcea561ab54ec13c1abcc2d7\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\cf75aa9b18f06d3b2335f904dba4e321\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\cf75aa9b18f06d3b2335f904dba4e321\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9db969efd78c626d748162acb38ce95f\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9db969efd78c626d748162acb38ce95f\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f54c7c84c5f67e8ee6537041e7fd1bab\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f54c7c84c5f67e8ee6537041e7fd1bab\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f921a27828947145d90f6239f7938681\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f921a27828947145d90f6239f7938681\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\e4dd37a652b916738c3c0bd41b505fa5\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.grpc:grpc-android:1.52.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\e4dd37a652b916738c3c0bd41b505fa5\transformed\grpc-android-1.52.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2a78d84440dd84ac3796151e98073ac9\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2a78d84440dd84ac3796151e98073ac9\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\43ed0d9722d084c7cfe8607187d06b67\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\43ed0d9722d084c7cfe8607187d06b67\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\788f745b4f6aaef48235a81abb54c6f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\788f745b4f6aaef48235a81abb54c6f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\456115ac9643a36b86fb7debc84af7cb\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\456115ac9643a36b86fb7debc84af7cb\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\305b1056f1eb0661a27cdbb3a0ef0b11\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\305b1056f1eb0661a27cdbb3a0ef0b11\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\6d92b110c97dce035831179f64032edc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\6d92b110c97dce035831179f64032edc\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\136186f76116a76577e933705066fb60\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\136186f76116a76577e933705066fb60\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\fd3c4a0a770afa92d3461d489257f995\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\fd3c4a0a770afa92d3461d489257f995\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b70b3d42a727a5a5f87762f38ae210e4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b70b3d42a727a5a5f87762f38ae210e4\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f72fe394f3b44b547edef032fe49296e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f72fe394f3b44b547edef032fe49296e\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\da7c2a225da80a1abbfe7639951eba2c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\da7c2a225da80a1abbfe7639951eba2c\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\8683e8e523293e238091f4971c33510d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\8683e8e523293e238091f4971c33510d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b5b44c1112c505878aa38e2feda16718\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b5b44c1112c505878aa38e2feda16718\transformed\protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b3984fce76b6cfba11eb213ff512d653\transformed\firebase-auth-22.3.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.10.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f864fe3c8f6f09f7c8e91182302cdc1b\transformed\firebase-firestore-24.10.0\AndroidManifest.xml:21:17-111
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ab54504f02fbc0c9ecfa74fbc5948c4d\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\788f745b4f6aaef48235a81abb54c6f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\788f745b4f6aaef48235a81abb54c6f6\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#com.example.todolist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#com.example.todolist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.2.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
