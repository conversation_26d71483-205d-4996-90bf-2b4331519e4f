<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="main_menu" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-anydpi\ic_launcher.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-anydpi\ic_launcher_round.xml" qualifiers="anydpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="background_pending">#FFF3E0</color><color name="primary_dark_color">#1976D2</color><color name="status_completed">#4CAF50</color><color name="text_hint">#BDBDBD</color><color name="text_secondary">#757575</color><color name="secondary_color">#FF9800</color><color name="status_pending">#FF9800</color><color name="background_completed">#E8F5E8</color><color name="accent_color">#4CAF50</color><color name="background_in_progress">#E3F2FD</color><color name="status_in_progress">#2196F3</color><color name="text_primary">#212121</color><color name="primary_color">#2196F3</color></file><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">To-Do List</string><string name="task_hint">Enter a new task</string><string name="add_task_button">Add Task</string><string name="clear_all_tasks">Clear All Tasks</string><string name="filter_by_status">Filter by Status</string><string name="add_new_task">Add New Task</string><string name="task_title">Task Title</string><string name="delete_task">Delete Task</string><string name="total_tasks">Total Tasks: %d</string><string name="no_tasks">No tasks found</string><string name="cancel">Cancel</string><string name="filter_tasks">Filter Tasks</string><string name="task_status">Task Status</string><string name="search_hint">Search by title or description</string><string name="clear_filter">Clear Filter</string><string name="apply_filter">Apply Filter</string><string name="edit_task">Edit Task</string><string name="filter">Filter</string><string name="status_all">All</string><string name="search_tasks">Search Tasks</string><string name="task_description">Task Description</string><string name="task_deleted">Task deleted successfully</string><string name="task_updated">Task updated successfully</string><string name="status_in_progress">In Progress</string><string name="status_completed">Completed</string><string name="status_pending">Pending</string><string name="error_empty_title">Please enter a task title</string><string name="error_empty_description">Please enter a task description</string></file><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Todolist" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style><style name="Theme.Todolist" parent="Base.Theme.Todolist"/></file><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Todolist" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="ic_calendar" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="status_background" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="task_item_background" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\task_item_background.xml" qualifiers="" type="drawable"/><file name="dialog_add_task" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml" qualifiers="" type="layout"/><file name="dialog_filter" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_filter.xml" qualifiers="" type="layout"/><file name="task_item" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>