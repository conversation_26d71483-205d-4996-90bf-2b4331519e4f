<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res"><file name="ic_calendar" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_calendar.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="status_background" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\status_background.xml" qualifiers="" type="drawable"/><file name="task_item_background" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\task_item_background.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="dialog_add_task" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_add_task.xml" qualifiers="" type="layout"/><file name="dialog_filter" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\dialog_filter.xml" qualifiers="" type="layout"/><file name="task_item" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\task_item.xml" qualifiers="" type="layout"/><file name="main_menu" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\colors.xml" qualifiers=""><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color><color name="primary_color">#2196F3</color><color name="primary_dark_color">#1976D2</color><color name="accent_color">#4CAF50</color><color name="secondary_color">#FF9800</color><color name="status_pending">#FF9800</color><color name="status_in_progress">#2196F3</color><color name="status_completed">#4CAF50</color><color name="background_pending">#FFF3E0</color><color name="background_in_progress">#E3F2FD</color><color name="background_completed">#E8F5E8</color><color name="text_primary">#212121</color><color name="text_secondary">#757575</color><color name="text_hint">#BDBDBD</color><color name="surface_color">#FFFFFF</color><color name="card_background">#FFFFFF</color><color name="divider_color">#E0E0E0</color></file><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Tapşırıq Siyahısı</string><string name="task_hint">Yeni tapşırıq daxil edin</string><string name="add_task_button">Tapşırıq Əlavə Et</string><string name="clear_all_tasks">Bütün Tapşırıqları Təmizlə</string><string name="add_new_task">Yeni Tapşırıq Əlavə Et</string><string name="task_title">Tapşırıq Başlığı</string><string name="task_description">Tapşırıq Açıqlaması</string><string name="task_status">Tapşırıq Statusu</string><string name="cancel">Ləğv Et</string><string name="filter">Filter</string><string name="filter_tasks">Tapşırıqları Filterlə</string><string name="filter_by_status">Statusa Görə Filterlə</string><string name="search_tasks">Tapşırıq Axtar</string><string name="search_hint">Başlıq və ya açıqlamaya görə axtar</string><string name="clear_filter">Filtri Təmizlə</string><string name="apply_filter">Filtri Tətbiq Et</string><string name="total_tasks">Ümumi Tapşırıqlar: %d</string><string name="edit_task">Tapşırığı Redaktə Et</string><string name="delete_task">Tapşırığı Sil</string><string name="task_updated">Tapşırıq uğurla yeniləndi</string><string name="task_deleted">Tapşırıq uğurla silindi</string><string name="no_tasks">Heç bir tapşırıq tapılmadı</string><string name="status_all">Hamısı</string><string name="status_pending">Gözləyir</string><string name="status_in_progress">Davam Edir</string><string name="status_completed">Tamamlandı</string><string name="error_empty_title">Zəhmət olmasa tapşırıq başlığı daxil edin</string><string name="error_empty_description">Zəhmət olmasa tapşırıq açıqlaması daxil edin</string><string name="clear_all_confirmation">Bütün tapşırıqları silmək istədiyinizə əminsiniz?</string><string name="task_added">Tapşırıq əlavə edildi!</string><string name="yes">Bəli</string><string name="no">Xeyr</string><string name="all_tasks_cleared">Bütün tapşırıqlar təmizləndi!</string><string name="refresh_tasks">Tapşırıqları Yenilə</string><string name="delete_confirmation">Bu tapşırığı silmək istədiyinizə əminsiniz?</string><string name="task_priority">Tapşırıq Prioriteti</string><string name="total_tasks_label">Ümumi Tapşırıqlar</string><string name="priority_high">Yüksək</string><string name="no_tasks_title">Tapşırıq yoxdur</string><string name="add_first_task">İlk Tapşırığı Əlavə Et</string><string name="priority_statistics">Prioritet Statistikası</string><string name="priority_low">Aşağı</string><string name="overall_statistics">Ümumi Statistika</string><string name="completion_rate">Tamamlanma Faizi</string><string name="no_tasks_subtitle">İlk tapşırığınızı əlavə etmək üçün aşağıdakı düyməyə basın</string><string name="priority_medium">Orta</string><string name="statistics">Statistika</string><string name="status_statistics">Status Statistikası</string></file><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values\themes.xml" qualifiers=""><style name="Base.Theme.Todolist" parent="Theme.AppCompat.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/surface_color</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style><style name="Theme.Todolist" parent="Base.Theme.Todolist"/></file><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Base.Theme.Todolist" parent="Theme.AppCompat.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/surface_color</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style><style name="Theme.Todolist" parent="Base.Theme.Todolist"/></file><file name="edit_text_background" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\edit_text_background.xml" qualifiers="" type="drawable"/><file path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="black">#FFFFFFFF</color><color name="white">#FF000000</color><color name="primary_color">#1976D2</color><color name="primary_dark_color">#0D47A1</color><color name="accent_color">#4CAF50</color><color name="secondary_color">#FF9800</color><color name="status_pending">#FFB74D</color><color name="status_in_progress">#64B5F6</color><color name="status_completed">#81C784</color><color name="background_pending">#3E2723</color><color name="background_in_progress">#1A237E</color><color name="background_completed">#1B5E20</color><color name="text_primary">#FFFFFF</color><color name="text_secondary">#B0BEC5</color><color name="text_hint">#78909C</color><color name="surface_color">#121212</color><color name="card_background">#1E1E1E</color><color name="divider_color">#2C2C2C</color></file><file name="fade_in" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\anim\fade_in.xml" qualifiers="" type="anim"/><file name="scale_in" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\anim\scale_in.xml" qualifiers="" type="anim"/><file name="slide_in_right" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\anim\slide_in_right.xml" qualifiers="" type="anim"/><file name="slide_out_left" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\anim\slide_out_left.xml" qualifiers="" type="anim"/><file name="ic_add" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_add.xml" qualifiers="" type="drawable"/><file name="ic_empty_tasks" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_empty_tasks.xml" qualifiers="" type="drawable"/><file name="ic_statistics" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\ic_statistics.xml" qualifiers="" type="drawable"/><file name="priority_background" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\drawable\priority_background.xml" qualifiers="" type="drawable"/><file name="activity_statistics" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\activity_statistics.xml" qualifiers="" type="layout"/><file name="empty_state" path="C:\Users\<USER>\Music\remote\todolist\app\src\main\res\layout\empty_state.xml" qualifiers="" type="layout"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>