<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_statistics"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/surface_color"
    tools:context=".StatisticsActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Overall Statistics Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            android:layout_marginBottom="16dp"
            android:backgroundTint="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/overall_statistics"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/totalTasksTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/total_tasks_label"
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/completionRateTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/completion_rate"
                    android:textSize="16sp"
                    android:textColor="@color/text_secondary" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Status Statistics Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            android:layout_marginBottom="16dp"
            android:backgroundTint="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/status_statistics"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/completedTasksTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/status_completed"
                    android:textSize="16sp"
                    android:textColor="@color/status_completed"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/pendingTasksTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/status_pending"
                    android:textSize="16sp"
                    android:textColor="@color/status_pending"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/inProgressTasksTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/status_in_progress"
                    android:textSize="16sp"
                    android:textColor="@color/status_in_progress" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- Priority Statistics Section -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp"
            android:backgroundTint="@color/card_background">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/priority_statistics"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/highPriorityTasksTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/priority_high"
                    android:textSize="16sp"
                    android:textColor="@color/error_color"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/mediumPriorityTasksTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/priority_medium"
                    android:textSize="16sp"
                    android:textColor="@color/warning_color"
                    android:layout_marginBottom="8dp" />

                <TextView
                    android:id="@+id/lowPriorityTasksTextView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/priority_low"
                    android:textSize="16sp"
                    android:textColor="@color/success_color" />

            </LinearLayout>
        </androidx.cardview.widget.CardView>

    </LinearLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
