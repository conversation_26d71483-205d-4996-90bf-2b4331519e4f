<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_color">#4CAF50</color>
    <color name="background_completed">#1B5E20</color>
    <color name="background_in_progress">#1A237E</color>
    <color name="background_pending">#3E2723</color>
    <color name="black">#FFFFFFFF</color>
    <color name="card_background">#1E1E1E</color>
    <color name="divider_color">#2C2C2C</color>
    <color name="primary_color">#1976D2</color>
    <color name="primary_dark_color">#0D47A1</color>
    <color name="secondary_color">#FF9800</color>
    <color name="status_completed">#81C784</color>
    <color name="status_in_progress">#64B5F6</color>
    <color name="status_pending">#FFB74D</color>
    <color name="surface_color">#121212</color>
    <color name="text_hint">#78909C</color>
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#B0BEC5</color>
    <color name="white">#FF000000</color>
    <style name="Base.Theme.Todolist" parent="Theme.AppCompat.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/surface_color</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>
    <style name="Theme.Todolist" parent="Base.Theme.Todolist"/>
</resources>