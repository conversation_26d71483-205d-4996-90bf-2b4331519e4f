package com.example.todolist;

import android.content.Context;
import android.content.SharedPreferences;
import android.graphics.Color;
import android.os.Bundle;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

public class StatisticsActivity extends AppCompatActivity {

    private TextView totalTasksTextView;
    private TextView completedTasksTextView;
    private TextView pendingTasksTextView;
    private TextView inProgressTasksTextView;
    private TextView highPriorityTasksTextView;
    private TextView mediumPriorityTasksTextView;
    private TextView lowPriorityTasksTextView;
    private TextView completionRateTextView;

    private ArrayList<Task> tasks;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_statistics);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main_statistics), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(getString(R.string.statistics));
        }

        initializeViews();
        loadTasks();
        displayStatistics();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadTasks();
        displayStatistics();
    }

    private void initializeViews() {
        totalTasksTextView = findViewById(R.id.totalTasksTextView);
        completedTasksTextView = findViewById(R.id.completedTasksTextView);
        pendingTasksTextView = findViewById(R.id.pendingTasksTextView);
        inProgressTasksTextView = findViewById(R.id.inProgressTasksTextView);
        highPriorityTasksTextView = findViewById(R.id.highPriorityTasksTextView);
        mediumPriorityTasksTextView = findViewById(R.id.mediumPriorityTasksTextView);
        lowPriorityTasksTextView = findViewById(R.id.lowPriorityTasksTextView);
        completionRateTextView = findViewById(R.id.completionRateTextView);
    }

    private void loadTasks() {
        SharedPreferences sharedPreferences = getSharedPreferences("MyTasks", Context.MODE_PRIVATE);
        Gson gson = new Gson();
        String json = sharedPreferences.getString("task_list", null);

        if (json != null && !json.isEmpty()) {
            Type type = new TypeToken<ArrayList<Task>>() {}.getType();
            tasks = gson.fromJson(json, type);
        } else {
            tasks = new ArrayList<>();
        }
    }

    private void displayStatistics() {
        if (tasks == null || tasks.isEmpty()) {
            Toast.makeText(this, "Statistika üçün tapşırıq yoxdur.", Toast.LENGTH_SHORT).show();
            totalTasksTextView.setText(getString(R.string.total_tasks_label) + ": 0");
            completedTasksTextView.setText(getString(R.string.status_completed) + ": 0");
            pendingTasksTextView.setText(getString(R.string.status_pending) + ": 0");
            inProgressTasksTextView.setText(getString(R.string.status_in_progress) + ": 0");
            highPriorityTasksTextView.setText(getString(R.string.priority_high) + ": 0");
            mediumPriorityTasksTextView.setText(getString(R.string.priority_medium) + ": 0");
            lowPriorityTasksTextView.setText(getString(R.string.priority_low) + ": 0");
            completionRateTextView.setText(getString(R.string.completion_rate) + ": 0%");
            return;
        }

        int totalTasks = tasks.size();
        int completedTasks = 0;
        int pendingTasks = 0;
        int inProgressTasks = 0;
        int highPriorityTasks = 0;
        int mediumPriorityTasks = 0;
        int lowPriorityTasks = 0;

        for (Task task : tasks) {
            switch (task.getStatus()) {
                case Task.STATUS_COMPLETED:
                    completedTasks++;
                    break;
                case Task.STATUS_PENDING:
                    pendingTasks++;
                    break;
                case Task.STATUS_IN_PROGRESS:
                    inProgressTasks++;
                    break;
            }

            switch (task.getPriority()) {
                case Task.PRIORITY_HIGH:
                    highPriorityTasks++;
                    break;
                case Task.PRIORITY_MEDIUM:
                    mediumPriorityTasks++;
                    break;
                case Task.PRIORITY_LOW:
                    lowPriorityTasks++;
                    break;
            }
        }

        double completionRate = (totalTasks > 0) ? ((double) completedTasks / totalTasks) * 100 : 0;

        totalTasksTextView.setText(getString(R.string.total_tasks_label) + ": " + totalTasks);
        completedTasksTextView.setText(getString(R.string.status_completed) + ": " + completedTasks);
        pendingTasksTextView.setText(getString(R.string.status_pending) + ": " + pendingTasks);
        inProgressTasksTextView.setText(getString(R.string.status_in_progress) + ": " + inProgressTasks);
        highPriorityTasksTextView.setText(getString(R.string.priority_high) + ": " + highPriorityTasks);
        mediumPriorityTasksTextView.setText(getString(R.string.priority_medium) + ": " + mediumPriorityTasks);
        lowPriorityTasksTextView.setText(getString(R.string.priority_low) + ": " + lowPriorityTasks);
        completionRateTextView.setText(getString(R.string.completion_rate) + ": " + String.format(Locale.getDefault(), "%.2f", completionRate) + "%");
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
