package com.example.todolist;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.Toolbar;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.ArrayList;

public class StatisticsActivity extends AppCompatActivity {

    private TextView totalTasksText;
    private TextView completedTasksText;
    private TextView pendingTasksText;
    private TextView inProgressTasksText;
    private TextView highPriorityText;
    private TextView mediumPriorityText;
    private TextView lowPriorityText;
    private TextView completionRateText;
    
    private ArrayList<Task> tasks;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_statistics);

        setupToolbar();
        initializeViews();
        loadTasks();
        calculateStatistics();
    }

    private void setupToolbar() {
        Toolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(getString(R.string.statistics));
        }
    }

    private void initializeViews() {
        totalTasksText = findViewById(R.id.totalTasksText);
        completedTasksText = findViewById(R.id.completedTasksText);
        pendingTasksText = findViewById(R.id.pendingTasksText);
        inProgressTasksText = findViewById(R.id.inProgressTasksText);
        highPriorityText = findViewById(R.id.highPriorityText);
        mediumPriorityText = findViewById(R.id.mediumPriorityText);
        lowPriorityText = findViewById(R.id.lowPriorityText);
        completionRateText = findViewById(R.id.completionRateText);
    }

    private void loadTasks() {
        SharedPreferences sharedPreferences = getSharedPreferences("MyTasks", Context.MODE_PRIVATE);
        Gson gson = new Gson();
        String json = sharedPreferences.getString("task_list", null);
        Type type = new TypeToken<ArrayList<Task>>() {}.getType();
        tasks = gson.fromJson(json, type);

        if (tasks == null) {
            tasks = new ArrayList<>();
        }
    }

    private void calculateStatistics() {
        int totalTasks = tasks.size();
        int completedTasks = 0;
        int pendingTasks = 0;
        int inProgressTasks = 0;
        int highPriorityTasks = 0;
        int mediumPriorityTasks = 0;
        int lowPriorityTasks = 0;

        for (Task task : tasks) {
            // Count by status
            switch (task.getStatus()) {
                case Task.STATUS_COMPLETED:
                    completedTasks++;
                    break;
                case Task.STATUS_PENDING:
                    pendingTasks++;
                    break;
                case Task.STATUS_IN_PROGRESS:
                    inProgressTasks++;
                    break;
            }

            // Count by priority
            switch (task.getPriority()) {
                case Task.PRIORITY_HIGH:
                    highPriorityTasks++;
                    break;
                case Task.PRIORITY_MEDIUM:
                    mediumPriorityTasks++;
                    break;
                case Task.PRIORITY_LOW:
                    lowPriorityTasks++;
                    break;
            }
        }

        // Calculate completion rate
        double completionRate = totalTasks > 0 ? (completedTasks * 100.0 / totalTasks) : 0;

        // Update UI
        totalTasksText.setText(String.valueOf(totalTasks));
        completedTasksText.setText(String.valueOf(completedTasks));
        pendingTasksText.setText(String.valueOf(pendingTasks));
        inProgressTasksText.setText(String.valueOf(inProgressTasks));
        highPriorityText.setText(String.valueOf(highPriorityTasks));
        mediumPriorityText.setText(String.valueOf(mediumPriorityTasks));
        lowPriorityText.setText(String.valueOf(lowPriorityTasks));
        completionRateText.setText(String.format("%.1f%%", completionRate));
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
