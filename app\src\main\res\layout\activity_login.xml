<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/gradient_primary"
    android:gravity="center"
    android:padding="32dp">

    <!-- App Logo/Icon -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_app_logo"
        android:layout_marginBottom="32dp"
        android:tint="@android:color/white" />

    <!-- App Title -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/app_name"
        android:textSize="32sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:layout_marginBottom="8dp"
        android:fontFamily="sans-serif-medium" />

    <!-- Subtitle -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login_subtitle"
        android:textSize="16sp"
        android:textColor="@android:color/white"
        android:alpha="0.8"
        android:layout_marginBottom="48dp"
        android:gravity="center" />

    <!-- Sign In Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:cardBackgroundColor="@color/card_background">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/sign_in_title"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@color/text_primary"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/sign_in_description"
                android:textSize="14sp"
                android:textColor="@color/text_secondary"
                android:gravity="center"
                android:layout_marginBottom="24dp" />

            <!-- Google Sign In Button -->
            <Button
                android:id="@+id/btnGoogleSignIn"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="@string/sign_in_with_google"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@drawable/google_sign_in_button"
                android:drawableStart="@drawable/ic_google"
                android:drawablePadding="12dp"
                android:gravity="center"
                android:layout_marginBottom="16dp" />

            <!-- Continue Without Account -->
            <Button
                android:id="@+id/btnContinueOffline"
                style="@style/Widget.AppCompat.Button.Borderless"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:text="@string/continue_without_account"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- Footer -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/login_footer"
        android:textSize="12sp"
        android:textColor="@android:color/white"
        android:alpha="0.6"
        android:layout_marginTop="32dp"
        android:gravity="center" />

</LinearLayout>
