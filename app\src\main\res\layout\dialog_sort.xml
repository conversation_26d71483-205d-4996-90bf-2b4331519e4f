<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/sort_tasks"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <RadioGroup
        android:id="@+id/radioGroupSort"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/radioSortDateCreated"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/sort_by_date_created"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/radioSortPriority"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/sort_by_priority"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/radioSortStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/sort_by_status"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/radioSortTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/sort_by_title"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/radioSortDeadline"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/sort_by_deadline"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

        <RadioButton
            android:id="@+id/radioSortCategory"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/sort_by_category"
            android:textSize="16sp"
            android:paddingVertical="8dp" />

    </RadioGroup>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/buttonCancelSort"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cancel"
            android:layout_marginEnd="8dp"
            android:background="?android:attr/selectableItemBackground" />

        <Button
            android:id="@+id/buttonApplySort"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/apply_filter" />

    </LinearLayout>

</LinearLayout>
