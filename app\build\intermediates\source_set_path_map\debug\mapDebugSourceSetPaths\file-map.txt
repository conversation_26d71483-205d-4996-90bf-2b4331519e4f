com.example.todolist.app-constraintlayout-2.2.1-0 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\0c850af394c9f19314b841fcb168ef45\transformed\constraintlayout-2.2.1\res
com.example.todolist.app-play-services-base-18.0.1-1 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1351b8c1d00e574e81c77e445d78a1cb\transformed\play-services-base-18.0.1\res
com.example.todolist.app-lifecycle-livedata-2.6.2-2 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1de238fcc0a42c2b587ebee7085bfcbd\transformed\lifecycle-livedata-2.6.2\res
com.example.todolist.app-emoji2-views-helper-1.3.0-3 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2342a6240f92a130700ffa9393d16894\transformed\emoji2-views-helper-1.3.0\res
com.example.todolist.app-cardview-1.0.0-4 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2a78d84440dd84ac3796151e98073ac9\transformed\cardview-1.0.0\res
com.example.todolist.app-recyclerview-1.1.0-5 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\41c3cd471c2d638d7f39c693813ff492\transformed\recyclerview-1.1.0\res
com.example.todolist.app-appcompat-1.7.0-6 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\47acb2d51539ebc7e1b6659adb48b4bc\transformed\appcompat-1.7.0\res
com.example.todolist.app-lifecycle-runtime-2.6.2-7 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\4f7064da9546fca51e7936f39df6eaeb\transformed\lifecycle-runtime-2.6.2\res
com.example.todolist.app-appcompat-resources-1.7.0-8 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\5d34a3e3ab9248f2d7a5fcbd72642d45\transformed\appcompat-resources-1.7.0\res
com.example.todolist.app-lifecycle-process-2.6.2-9 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\684ac3a1fbedf11cc466fc4168cc60c6\transformed\lifecycle-process-2.6.2\res
com.example.todolist.app-startup-runtime-1.1.1-10 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\788f745b4f6aaef48235a81abb54c6f6\transformed\startup-runtime-1.1.1\res
com.example.todolist.app-coordinatorlayout-1.1.0-11 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7ce5cf13d729ed5aad66ee3a230fd592\transformed\coordinatorlayout-1.1.0\res
com.example.todolist.app-lifecycle-livedata-core-2.6.2-12 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7fe705e45fb23f665c7eb8611efc4f85\transformed\lifecycle-livedata-core-2.6.2\res
com.example.todolist.app-profileinstaller-1.4.0-13 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\86811bcd163c90c89f77ec20b504c497\transformed\profileinstaller-1.4.0\res
com.example.todolist.app-annotation-experimental-1.4.0-14 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\8683e8e523293e238091f4971c33510d\transformed\annotation-experimental-1.4.0\res
com.example.todolist.app-core-1.13.0-15 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\94b25b2e0232f11eacc589061e7c8810\transformed\core-1.13.0\res
com.example.todolist.app-viewpager2-1.0.0-16 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\9646aad0dd6e4672fd7ce77e800ba944\transformed\viewpager2-1.0.0\res
com.example.todolist.app-drawerlayout-1.1.1-17 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\98b61775cad0d12ad38905c0b2409c01\transformed\drawerlayout-1.1.1\res
com.example.todolist.app-swiperefreshlayout-1.1.0-18 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b52ed8f98cafa40fd271bfeff22227aa\transformed\swiperefreshlayout-1.1.0\res
com.example.todolist.app-core-runtime-2.2.0-19 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b70b3d42a727a5a5f87762f38ae210e4\transformed\core-runtime-2.2.0\res
com.example.todolist.app-transition-1.5.0-20 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b840c59d10651504a233ff733d9b47af\transformed\transition-1.5.0\res
com.example.todolist.app-emoji2-1.3.0-21 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\bc794d9ea39e5293226701ab749b7083\transformed\emoji2-1.3.0\res
com.example.todolist.app-lifecycle-viewmodel-savedstate-2.6.2-22 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c3492ee4dcea561ab54ec13c1abcc2d7\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.todolist.app-play-services-auth-20.7.0-23 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c9a0f2761afc4821249a633714b5895f\transformed\play-services-auth-20.7.0\res
com.example.todolist.app-browser-1.4.0-24 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ccc6704ec40174d3a5896d53e2b7c03a\transformed\browser-1.4.0\res
com.example.todolist.app-core-ktx-1.13.0-25 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\cdd562ae3d28555dc6385964e68e86da\transformed\core-ktx-1.13.0\res
com.example.todolist.app-savedstate-1.2.1-26 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\cf75aa9b18f06d3b2335f904dba4e321\transformed\savedstate-1.2.1\res
com.example.todolist.app-firebase-common-20.4.2-27 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d75593877f61c0336762077ba42cdf39\transformed\firebase-common-20.4.2\res
com.example.todolist.app-material-1.12.0-28 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d8ec8ac963a493a193201cc8ee075ff5\transformed\material-1.12.0\res
com.example.todolist.app-play-services-basement-18.2.0-29 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\edf9bf37f6d93d447fb63ea57318ab6f\transformed\play-services-basement-18.2.0\res
com.example.todolist.app-fragment-1.5.4-30 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f54c7c84c5f67e8ee6537041e7fd1bab\transformed\fragment-1.5.4\res
com.example.todolist.app-activity-1.8.2-31 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f921a27828947145d90f6239f7938681\transformed\activity-1.8.2\res
com.example.todolist.app-lifecycle-viewmodel-2.6.2-32 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\ffbe0b8035152d3bc28eee4116464488\transformed\lifecycle-viewmodel-2.6.2\res
com.example.todolist.app-pngs-33 C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\pngs\debug
com.example.todolist.app-res-34 C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\processDebugGoogleServices
com.example.todolist.app-resValues-35 C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\resValues\debug
com.example.todolist.app-packageDebugResources-36 C:\Users\<USER>\Music\remote\todolist\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.todolist.app-packageDebugResources-37 C:\Users\<USER>\Music\remote\todolist\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.todolist.app-debug-38 C:\Users\<USER>\Music\remote\todolist\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.todolist.app-debug-39 C:\Users\<USER>\Music\remote\todolist\app\src\debug\res
com.example.todolist.app-main-40 C:\Users\<USER>\Music\remote\todolist\app\src\main\res
