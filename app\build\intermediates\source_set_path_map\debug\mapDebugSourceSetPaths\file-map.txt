com.example.todolist.app-core-ktx-1.13.0-0 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\01c7b1faba9adde3226e03fbf599f8a1\transformed\core-ktx-1.13.0\res
com.example.todolist.app-lifecycle-runtime-2.6.2-1 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\1cffafd388abeab9d49a4f12b48ffd86\transformed\lifecycle-runtime-2.6.2\res
com.example.todolist.app-core-runtime-2.2.0-2 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2141adb33ff87d8e2a2b6d001265e283\transformed\core-runtime-2.2.0\res
com.example.todolist.app-lifecycle-viewmodel-2.6.2-3 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\2272a584a9bcc231287e09a9f17b04a0\transformed\lifecycle-viewmodel-2.6.2\res
com.example.todolist.app-appcompat-resources-1.7.0-4 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\395a0e537652e3a227b23db64a87a746\transformed\appcompat-resources-1.7.0\res
com.example.todolist.app-lifecycle-livedata-2.6.2-5 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\3f245b772145b77b82a23aa9aeb612e4\transformed\lifecycle-livedata-2.6.2\res
com.example.todolist.app-cardview-1.0.0-6 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\400c40ef69e546a1b5c55ff10776b7a1\transformed\cardview-1.0.0\res
com.example.todolist.app-emoji2-1.3.0-7 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\49db857962f75b674f129af70793bf92\transformed\emoji2-1.3.0\res
com.example.todolist.app-drawerlayout-1.1.1-8 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\4b57b8b39224dae7ca352069610b013c\transformed\drawerlayout-1.1.1\res
com.example.todolist.app-lifecycle-viewmodel-savedstate-2.6.2-9 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\639b02e9095830450af828a9c619049b\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.example.todolist.app-startup-runtime-1.1.1-10 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\70b9ddfa12762bcada556e1c4ee55aba\transformed\startup-runtime-1.1.1\res
com.example.todolist.app-recyclerview-1.1.0-11 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7451c08c66f880a75221883c985222cd\transformed\recyclerview-1.1.0\res
com.example.todolist.app-activity-1.8.2-12 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\7e892d9eeab5913f01aef55eb3ba6e03\transformed\activity-1.8.2\res
com.example.todolist.app-annotation-experimental-1.4.0-13 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\80b609f5b17bb39d8dd617f4dca41cb9\transformed\annotation-experimental-1.4.0\res
com.example.todolist.app-coordinatorlayout-1.1.0-14 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\8163d829a9240a624d84151c79e57aa5\transformed\coordinatorlayout-1.1.0\res
com.example.todolist.app-lifecycle-livedata-core-2.6.2-15 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\82896e4a6c794c46f2bd812060e631ec\transformed\lifecycle-livedata-core-2.6.2\res
com.example.todolist.app-savedstate-1.2.1-16 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a4e0145e3ed20abe1359f32a68cbdd20\transformed\savedstate-1.2.1\res
com.example.todolist.app-lifecycle-process-2.6.2-17 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a7566ef00b897f386246b210a3a7334c\transformed\lifecycle-process-2.6.2\res
com.example.todolist.app-viewpager2-1.0.0-18 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a898053dfe38ee57ca0bbdf36c0c3b4a\transformed\viewpager2-1.0.0\res
com.example.todolist.app-constraintlayout-2.2.1-19 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b0b380acad4039e1d3bdfa9c7b413d12\transformed\constraintlayout-2.2.1\res
com.example.todolist.app-core-1.13.0-20 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b11727be95d45a60cc530c69ce7a2e71\transformed\core-1.13.0\res
com.example.todolist.app-profileinstaller-1.4.0-21 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\res
com.example.todolist.app-fragment-1.5.4-22 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c2debf6b97212766e246590245856d47\transformed\fragment-1.5.4\res
com.example.todolist.app-emoji2-views-helper-1.3.0-23 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\c3245fc1bde9029d8e77806e2aaa30d1\transformed\emoji2-views-helper-1.3.0\res
com.example.todolist.app-swiperefreshlayout-1.1.0-24 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\d8b31353ff0cbab52623732a26ff4231\transformed\swiperefreshlayout-1.1.0\res
com.example.todolist.app-material-1.12.0-25 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\e2a7f8f4879097be8d4279b82ba3ae42\transformed\material-1.12.0\res
com.example.todolist.app-transition-1.5.0-26 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\f2fc6135bb23f8a9cc3414a08ebb66ff\transformed\transition-1.5.0\res
com.example.todolist.app-appcompat-1.7.0-27 C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\fe0ea9fff5dbc0ebd57bd6ccf93c901b\transformed\appcompat-1.7.0\res
com.example.todolist.app-pngs-28 C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\pngs\debug
com.example.todolist.app-resValues-29 C:\Users\<USER>\Music\remote\todolist\app\build\generated\res\resValues\debug
com.example.todolist.app-packageDebugResources-30 C:\Users\<USER>\Music\remote\todolist\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.example.todolist.app-packageDebugResources-31 C:\Users\<USER>\Music\remote\todolist\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.example.todolist.app-debug-32 C:\Users\<USER>\Music\remote\todolist\app\build\intermediates\merged_res\debug\mergeDebugResources
com.example.todolist.app-debug-33 C:\Users\<USER>\Music\remote\todolist\app\src\debug\res
com.example.todolist.app-main-34 C:\Users\<USER>\Music\remote\todolist\app\src\main\res
