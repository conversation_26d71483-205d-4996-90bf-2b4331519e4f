package com.example.todolist;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.google.android.gms.auth.api.signin.GoogleSignIn;
import com.google.android.gms.auth.api.signin.GoogleSignInAccount;
import com.google.android.gms.auth.api.signin.GoogleSignInClient;
import com.google.android.gms.auth.api.signin.GoogleSignInOptions;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.auth.AuthCredential;
import com.google.firebase.auth.AuthResult;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseUser;
import com.google.firebase.auth.GoogleAuthProvider;

public class <PERSON><PERSON>uthHelper {
    
    private static final int RC_SIGN_IN = 9001;
    private FirebaseAuth mAuth;
    private GoogleSignInClient mGoogleSignInClient;
    private Context context;
    private AuthListener authListener;
    
    public interface AuthListener {
        void onSignInSuccess(FirebaseUser user);
        void onSignInFailure(String error);
        void onSignOutSuccess();
    }
    
    public GoogleAuthHelper(Context context, AuthListener listener) {
        this.context = context;
        this.authListener = listener;
        
        // Initialize Firebase Auth
        mAuth = FirebaseAuth.getInstance();
        
        // Configure Google Sign In
        GoogleSignInOptions gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestIdToken(context.getString(R.string.default_web_client_id))
                .requestEmail()
                .build();
        
        mGoogleSignInClient = GoogleSignIn.getClient(context, gso);
    }
    
    public void signIn() {
        Intent signInIntent = mGoogleSignInClient.getSignInIntent();
        ((Activity) context).startActivityForResult(signInIntent, RC_SIGN_IN);
    }
    
    public void signOut() {
        // Firebase sign out
        mAuth.signOut();
        
        // Google sign out
        mGoogleSignInClient.signOut().addOnCompleteListener((Activity) context, new OnCompleteListener<Void>() {
            @Override
            public void onComplete(@NonNull Task<Void> task) {
                if (authListener != null) {
                    authListener.onSignOutSuccess();
                }
                Toast.makeText(context, "Hesabdan çıxış edildi", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    public void handleSignInResult(Intent data) {
        Task<GoogleSignInAccount> task = GoogleSignIn.getSignedInAccountFromIntent(data);
        try {
            GoogleSignInAccount account = task.getResult(ApiException.class);
            firebaseAuthWithGoogle(account.getIdToken());
        } catch (ApiException e) {
            if (authListener != null) {
                authListener.onSignInFailure("Google Sign-In xətası: " + e.getMessage());
            }
        }
    }
    
    private void firebaseAuthWithGoogle(String idToken) {
        AuthCredential credential = GoogleAuthProvider.getCredential(idToken, null);
        mAuth.signInWithCredential(credential)
                .addOnCompleteListener((Activity) context, new OnCompleteListener<AuthResult>() {
                    @Override
                    public void onComplete(@NonNull Task<AuthResult> task) {
                        if (task.isSuccessful()) {
                            FirebaseUser user = mAuth.getCurrentUser();
                            if (authListener != null) {
                                authListener.onSignInSuccess(user);
                            }
                            Toast.makeText(context, "Giriş uğurlu: " + user.getDisplayName(), Toast.LENGTH_SHORT).show();
                        } else {
                            if (authListener != null) {
                                authListener.onSignInFailure("Firebase authentication xətası");
                            }
                        }
                    }
                });
    }
    
    public FirebaseUser getCurrentUser() {
        return mAuth.getCurrentUser();
    }
    
    public boolean isSignedIn() {
        return getCurrentUser() != null;
    }
    
    public String getUserId() {
        FirebaseUser user = getCurrentUser();
        return user != null ? user.getUid() : null;
    }
    
    public String getUserName() {
        FirebaseUser user = getCurrentUser();
        return user != null ? user.getDisplayName() : "Anonim";
    }
    
    public String getUserEmail() {
        FirebaseUser user = getCurrentUser();
        return user != null ? user.getEmail() : "";
    }
    
    public static int getSignInRequestCode() {
        return RC_SIGN_IN;
    }
}
