R_DEF: Internal format may change without notice
local
anim fade_in
anim scale_in
anim slide_in_right
anim slide_out_left
color accent_color
color background_completed
color background_in_progress
color background_pending
color black
color card_background
color divider_color
color primary_color
color primary_dark_color
color secondary_color
color status_completed
color status_in_progress
color status_pending
color surface_color
color text_hint
color text_primary
color text_secondary
color white
drawable edit_text_background
drawable ic_add
drawable ic_calendar
drawable ic_empty_tasks
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_statistics
drawable priority_background
drawable status_background
drawable task_item_background
id action_clear_all
id action_statistics
id buttonAdd
id buttonAddFirstTask
id buttonApplyFilter
id buttonCancel
id buttonClearFilter
id completedTasksText
id completionRateText
id editTextDescription
id editTextSearch
id editTextTitle
id emptyStateView
id fabAddTask
id filterButton
id highPriorityText
id inProgressTasksText
id lowPriorityText
id main
id mediumPriorityText
id pendingTasksText
id priorityIndicator
id spinnerFilterStatus
id spinnerPriority
id spinnerStatus
id swipeRefreshLayout
id taskDate
id taskDescription
id taskPriority
id taskStatus
id taskTitle
id tasksCountTextView
id tasksListView
id toolbar
id totalTasksText
layout activity_main
layout activity_statistics
layout dialog_add_task
layout dialog_filter
layout empty_state
layout task_item
menu main_menu
mipmap ic_launcher
mipmap ic_launcher_round
string add_first_task
string add_new_task
string add_task_button
string all_tasks_cleared
string app_name
string apply_filter
string cancel
string clear_all_confirmation
string clear_all_tasks
string clear_filter
string completion_rate
string delete_confirmation
string delete_task
string edit_task
string error_empty_description
string error_empty_title
string filter
string filter_by_status
string filter_tasks
string no
string no_tasks
string no_tasks_subtitle
string no_tasks_title
string overall_statistics
string priority_high
string priority_low
string priority_medium
string priority_statistics
string refresh_tasks
string search_hint
string search_tasks
string statistics
string status_all
string status_completed
string status_in_progress
string status_pending
string status_statistics
string task_added
string task_deleted
string task_description
string task_hint
string task_priority
string task_status
string task_title
string task_updated
string total_tasks
string total_tasks_label
string yes
style Base.Theme.Todolist
style Theme.Todolist
