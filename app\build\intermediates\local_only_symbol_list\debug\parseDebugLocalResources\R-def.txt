R_DEF: Internal format may change without notice
local
anim fade_in
anim scale_in
anim slide_in_right
anim slide_out_left
color accent_color
color background_completed
color background_in_progress
color background_pending
color black
color card_background
color divider_color
color error_50
color error_500
color error_600
color error_color
color neutral_0
color neutral_100
color neutral_200
color neutral_300
color neutral_400
color neutral_50
color neutral_500
color neutral_600
color neutral_700
color neutral_800
color neutral_900
color neutral_950
color primary_100
color primary_200
color primary_300
color primary_400
color primary_50
color primary_500
color primary_600
color primary_700
color primary_800
color primary_900
color primary_950
color primary_color
color primary_dark_color
color secondary_color
color status_completed
color status_in_progress
color status_pending
color success_50
color success_500
color success_600
color success_color
color surface_color
color text_hint
color text_primary
color text_secondary
color warning_50
color warning_500
color warning_600
color warning_color
color white
drawable edit_text_background
drawable fab_background
drawable google_sign_in_button
drawable gradient_accent
drawable gradient_primary
drawable ic_account
drawable ic_add
drawable ic_app_logo
drawable ic_calendar
drawable ic_empty_tasks
drawable ic_filter
drawable ic_google
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_notification
drawable ic_sort
drawable ic_statistics
drawable modern_card_background
drawable priority_background
drawable status_background
drawable task_item_background
font inter_bold
id action_account
id action_backup
id action_clear_all
id action_restore
id action_sort
id action_statistics
id btnContinueOffline
id btnGoogleSignIn
id buttonAdd
id buttonAddFirstTask
id buttonApplyFilter
id buttonApplySort
id buttonCancel
id buttonCancelSort
id buttonClearFilter
id completedTasksTextView
id completionRateTextView
id editTextDeadline
id editTextDescription
id editTextReminder
id editTextSearch
id editTextTitle
id emptyStateView
id fabAddTask
id filterButton
id highPriorityTasksTextView
id imageViewCalendar
id imageViewReminderCalendar
id inProgressTasksTextView
id lowPriorityTasksTextView
id main
id main_statistics
id mediumPriorityTasksTextView
id pendingTasksTextView
id priorityIndicator
id radioGroupSort
id radioSortCategory
id radioSortDateCreated
id radioSortDeadline
id radioSortPriority
id radioSortStatus
id radioSortTitle
id reminderLayout
id spinnerCategory
id spinnerFilterStatus
id spinnerPriority
id spinnerStatus
id swipeRefreshLayout
id taskCompletedCheckbox
id taskDate
id taskDeadline
id taskDescription
id taskPriority
id taskReminderTime
id taskStatus
id taskTitle
id tasksCountTextView
id tasksListView
id toolbar
id totalTasksTextView
layout activity_login
layout activity_main
layout activity_statistics
layout dialog_add_task
layout dialog_filter
layout dialog_sort
layout empty_state
layout task_item
menu main_menu
mipmap ic_launcher
mipmap ic_launcher_round
string account_menu
string add_first_task
string add_new_task
string add_task_button
string all_tasks_cleared
string app_name
string apply_filter
string backup_export
string backup_import
string cancel
string category_education
string category_health
string category_other
string category_personal
string category_shopping
string category_work
string clear_all_confirmation
string clear_all_tasks
string clear_filter
string completion_rate
string continue_without_account
string default_web_client_id
string delete_confirmation
string delete_task
string edit_task
string error_empty_description
string error_empty_title
string filter
string filter_by_status
string filter_tasks
string gcm_defaultSenderId
string google_api_key
string google_app_id
string google_crash_reporting_api_key
string google_storage_bucket
string login_footer
string login_subtitle
string no
string no_tasks
string no_tasks_subtitle
string no_tasks_title
string overall_statistics
string priority_high
string priority_low
string priority_medium
string priority_statistics
string project_id
string refresh_tasks
string search_hint
string search_tasks
string select_deadline
string select_reminder_time
string sign_in_description
string sign_in_title
string sign_in_with_google
string sign_out
string signed_in_as
string sort_by_category
string sort_by_date_created
string sort_by_deadline
string sort_by_priority
string sort_by_status
string sort_by_title
string sort_tasks
string statistics
string status_all
string status_completed
string status_in_progress
string status_pending
string status_statistics
string sync_failed
string sync_now
string sync_success
string task_added
string task_category
string task_deadline
string task_deleted
string task_description
string task_hint
string task_priority
string task_reminder
string task_status
string task_title
string task_updated
string total_tasks
string total_tasks_label
string yes
style Base.Theme.Todolist
style Theme.Todolist
