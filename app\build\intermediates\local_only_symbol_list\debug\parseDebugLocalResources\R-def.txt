R_DEF: Internal format may change without notice
local
color accent_color
color background_completed
color background_in_progress
color background_pending
color black
color primary_color
color primary_dark_color
color secondary_color
color status_completed
color status_in_progress
color status_pending
color text_hint
color text_primary
color text_secondary
color white
drawable edit_text_background
drawable ic_calendar
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable status_background
drawable task_item_background
id action_clear_all
id addTaskButton
id buttonAdd
id buttonApplyFilter
id buttonCancel
id buttonClearFilter
id editTextDescription
id editTextSearch
id editTextTitle
id filterButton
id main
id spinnerFilterStatus
id spinnerStatus
id taskDate
id taskDescription
id taskStatus
id taskTitle
id tasksCountTextView
id tasksListView
layout activity_main
layout dialog_add_task
layout dialog_filter
layout task_item
menu main_menu
mipmap ic_launcher
mipmap ic_launcher_round
string add_new_task
string add_task_button
string app_name
string apply_filter
string cancel
string clear_all_tasks
string clear_filter
string delete_task
string edit_task
string error_empty_description
string error_empty_title
string filter
string filter_by_status
string filter_tasks
string no_tasks
string search_hint
string search_tasks
string status_all
string status_completed
string status_in_progress
string status_pending
string task_deleted
string task_description
string task_hint
string task_status
string task_title
string task_updated
string total_tasks
style Base.Theme.Todolist
style Theme.Todolist
