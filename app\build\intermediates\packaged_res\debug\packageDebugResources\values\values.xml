<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_color">#4CAF50</color>
    <color name="background_completed">#E8F5E8</color>
    <color name="background_in_progress">#E3F2FD</color>
    <color name="background_pending">#FFF3E0</color>
    <color name="black">#FF000000</color>
    <color name="card_background">#FFFFFF</color>
    <color name="divider_color">#E0E0E0</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark_color">#1976D2</color>
    <color name="secondary_color">#FF9800</color>
    <color name="status_completed">#4CAF50</color>
    <color name="status_in_progress">#2196F3</color>
    <color name="status_pending">#FF9800</color>
    <color name="surface_color">#FFFFFF</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="white">#FFFFFFFF</color>
    <string name="add_first_task">İlk Tapşırığı Əlavə Et</string>
    <string name="add_new_task">Yeni Tapşırıq Əlavə Et</string>
    <string name="add_task_button">Tapşırıq Əlavə Et</string>
    <string name="all_tasks_cleared">Bütün tapşırıqlar təmizləndi!</string>
    <string name="app_name">Tapşırıq Siyahısı</string>
    <string name="apply_filter">Filtri Tətbiq Et</string>
    <string name="cancel">Ləğv Et</string>
    <string name="clear_all_confirmation">Bütün tapşırıqları silmək istədiyinizə əminsiniz?</string>
    <string name="clear_all_tasks">Bütün Tapşırıqları Təmizlə</string>
    <string name="clear_filter">Filtri Təmizlə</string>
    <string name="completion_rate">Tamamlanma Faizi</string>
    <string name="delete_confirmation">Bu tapşırığı silmək istədiyinizə əminsiniz?</string>
    <string name="delete_task">Tapşırığı Sil</string>
    <string name="edit_task">Tapşırığı Redaktə Et</string>
    <string name="error_empty_description">Zəhmət olmasa tapşırıq açıqlaması daxil edin</string>
    <string name="error_empty_title">Zəhmət olmasa tapşırıq başlığı daxil edin</string>
    <string name="filter">Filter</string>
    <string name="filter_by_status">Statusa Görə Filterlə</string>
    <string name="filter_tasks">Tapşırıqları Filterlə</string>
    <string name="no">Xeyr</string>
    <string name="no_tasks">Heç bir tapşırıq tapılmadı</string>
    <string name="no_tasks_subtitle">İlk tapşırığınızı əlavə etmək üçün aşağıdakı düyməyə basın</string>
    <string name="no_tasks_title">Tapşırıq yoxdur</string>
    <string name="overall_statistics">Ümumi Statistika</string>
    <string name="priority_high">Yüksək</string>
    <string name="priority_low">Aşağı</string>
    <string name="priority_medium">Orta</string>
    <string name="priority_statistics">Prioritet Statistikası</string>
    <string name="refresh_tasks">Tapşırıqları Yenilə</string>
    <string name="search_hint">Başlıq və ya açıqlamaya görə axtar</string>
    <string name="search_tasks">Tapşırıq Axtar</string>
    <string name="statistics">Statistika</string>
    <string name="status_all">Hamısı</string>
    <string name="status_completed">Tamamlandı</string>
    <string name="status_in_progress">Davam Edir</string>
    <string name="status_pending">Gözləyir</string>
    <string name="status_statistics">Status Statistikası</string>
    <string name="task_added">Tapşırıq əlavə edildi!</string>
    <string name="task_deleted">Tapşırıq uğurla silindi</string>
    <string name="task_description">Tapşırıq Açıqlaması</string>
    <string name="task_hint">Yeni tapşırıq daxil edin</string>
    <string name="task_priority">Tapşırıq Prioriteti</string>
    <string name="task_status">Tapşırıq Statusu</string>
    <string name="task_title">Tapşırıq Başlığı</string>
    <string name="task_updated">Tapşırıq uğurla yeniləndi</string>
    <string name="total_tasks">Ümumi Tapşırıqlar: %d</string>
    <string name="total_tasks_label">Ümumi Tapşırıqlar</string>
    <string name="yes">Bəli</string>
    <style name="Base.Theme.Todolist" parent="Theme.AppCompat.DayNight">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/surface_color</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>
    <style name="Theme.Todolist" parent="Base.Theme.Todolist"/>
</resources>