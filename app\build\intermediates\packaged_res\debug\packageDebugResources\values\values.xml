<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="accent_color">#4CAF50</color>
    <color name="background_completed">#E8F5E8</color>
    <color name="background_in_progress">#E3F2FD</color>
    <color name="background_pending">#FFF3E0</color>
    <color name="black">#FF000000</color>
    <color name="primary_color">#2196F3</color>
    <color name="primary_dark_color">#1976D2</color>
    <color name="secondary_color">#FF9800</color>
    <color name="status_completed">#4CAF50</color>
    <color name="status_in_progress">#2196F3</color>
    <color name="status_pending">#FF9800</color>
    <color name="text_hint">#BDBDBD</color>
    <color name="text_primary">#212121</color>
    <color name="text_secondary">#757575</color>
    <color name="white">#FFFFFFFF</color>
    <string name="add_new_task">Add New Task</string>
    <string name="add_task_button">Add Task</string>
    <string name="app_name">To-Do List</string>
    <string name="apply_filter">Apply Filter</string>
    <string name="cancel">Cancel</string>
    <string name="clear_all_tasks">Clear All Tasks</string>
    <string name="clear_filter">Clear Filter</string>
    <string name="delete_task">Delete Task</string>
    <string name="edit_task">Edit Task</string>
    <string name="error_empty_description">Please enter a task description</string>
    <string name="error_empty_title">Please enter a task title</string>
    <string name="filter">Filter</string>
    <string name="filter_by_status">Filter by Status</string>
    <string name="filter_tasks">Filter Tasks</string>
    <string name="no_tasks">No tasks found</string>
    <string name="search_hint">Search by title or description</string>
    <string name="search_tasks">Search Tasks</string>
    <string name="status_all">All</string>
    <string name="status_completed">Completed</string>
    <string name="status_in_progress">In Progress</string>
    <string name="status_pending">Pending</string>
    <string name="task_deleted">Task deleted successfully</string>
    <string name="task_description">Task Description</string>
    <string name="task_hint">Enter a new task</string>
    <string name="task_status">Task Status</string>
    <string name="task_title">Task Title</string>
    <string name="task_updated">Task updated successfully</string>
    <string name="total_tasks">Total Tasks: %d</string>
    <style name="Base.Theme.Todolist" parent="Theme.AppCompat.Light.DarkActionBar">
        
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
    </style>
    <style name="Theme.Todolist" parent="Base.Theme.Todolist"/>
</resources>