<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme for light mode -->
    <style name="Base.Theme.Todolist" parent="Theme.AppCompat.DayNight">
        <!-- Light mode colors -->
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark_color</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/surface_color</item>
        <item name="android:textColorPrimary">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>
    </style>

    <style name="Theme.Todolist" parent="Base.Theme.Todolist" />
</resources>