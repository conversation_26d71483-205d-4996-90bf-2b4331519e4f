<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/filter_tasks"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/filter_by_status"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/spinnerFilterStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:minHeight="48dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/search_tasks"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:layout_marginBottom="8dp" />

    <EditText
        android:id="@+id/editTextSearch"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:hint="@string/search_hint"
        android:inputType="text"
        android:maxLines="1"
        android:minHeight="48dp"
        android:padding="12dp"
        android:background="@drawable/edit_text_background" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/buttonClearFilter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/clear_filter"
            android:layout_marginEnd="8dp"
            android:background="?android:attr/selectableItemBackground" />

        <Button
            android:id="@+id/buttonApplyFilter"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/apply_filter" />

    </LinearLayout>

</LinearLayout>
