<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="@color/neutral_0"
    android:foreground="?android:attr/selectableItemBackground">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <CheckBox
                android:id="@+id/taskCompletedCheckbox"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:focusable="false"
                android:clickable="false" />

            <View
                android:id="@+id/priorityIndicator"
                android:layout_width="6dp"
                android:layout_height="match_parent"
                android:background="@color/primary_600"
                android:layout_marginEnd="12dp" />

            <TextView
                android:id="@+id/taskTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:ellipsize="end"
                android:fontFamily="@font/inter_medium"
                android:maxLines="2"
                android:text="Task Title"
                android:textColor="@color/neutral_900"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/taskPriority"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Orta"
                android:textSize="10sp"
                android:textStyle="bold"
                android:paddingHorizontal="6dp"
                android:paddingVertical="2dp"
                android:background="@drawable/priority_background"
                android:textColor="@color/neutral_0"
                android:layout_marginStart="8dp" />

            <TextView
                android:id="@+id/taskStatus"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@drawable/status_background"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"
                android:text="Pending"
                android:textColor="@color/neutral_900"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <TextView
            android:id="@+id/taskDescription"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Task description goes here..."
            android:textSize="14sp"
            android:textColor="@color/neutral_700"
            android:layout_marginTop="4dp"
            android:maxLines="3"
            android:ellipsize="end" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical">

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_calendar"
                app:tint="@color/neutral_500"
                android:contentDescription="@string/task_deadline" />

            <TextView
                android:id="@+id/taskDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="01/01/2024 12:00"
                android:textSize="12sp"
                android:textColor="@color/neutral_600"
                android:layout_marginStart="4dp" />

            <TextView
                android:id="@+id/taskDeadline"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Deadline: Yoxdur"
                android:textSize="12sp"
                android:textColor="@color/neutral_600"
                android:gravity="end"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/reminderLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp"
            android:gravity="center_vertical"
            android:visibility="gone"> <!-- Initially hidden -->

            <ImageView
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:src="@drawable/ic_notification"
                app:tint="@color/neutral_500"
                android:contentDescription="@string/task_reminder" />

            <TextView
                android:id="@+id/taskReminderTime"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Reminder: 01/01/2024 11:00"
                android:textSize="12sp"
                android:textColor="@color/neutral_600"
                android:layout_marginStart="4dp" />
        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>
