<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="12dp"
    android:layout_margin="4dp"
    android:background="@drawable/task_item_background"
    android:elevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <View
            android:id="@+id/priorityIndicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="@color/status_pending"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/taskTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Task Title"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/text_primary"
            android:maxLines="2"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/taskPriority"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Orta"
            android:textSize="10sp"
            android:textStyle="bold"
            android:padding="2dp"
            android:background="@drawable/priority_background"
            android:textColor="@android:color/white"
            android:layout_marginEnd="4dp" />

        <TextView
            android:id="@+id/taskStatus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Pending"
            android:textSize="12sp"
            android:textStyle="bold"
            android:padding="4dp"
            android:background="@drawable/status_background"
            android:textColor="@android:color/white"
            android:layout_marginStart="4dp" />

    </LinearLayout>

    <TextView
        android:id="@+id/taskDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Task description goes here..."
        android:textSize="14sp"
        android:textColor="@color/text_secondary"
        android:layout_marginTop="4dp"
        android:maxLines="3"
        android:ellipsize="end" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_calendar"
            android:tint="@color/text_secondary" />

        <TextView
            android:id="@+id/taskDate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="01/01/2024 12:00"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginStart="4dp" />

    </LinearLayout>

</LinearLayout>
