{"logs": [{"outputFile": "com.example.todolist.app-mergeDebugResources-30:/values-night-v8/values-night-v8.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.9\\transforms\\fe0ea9fff5dbc0ebd57bd6ccf93c901b\\transformed\\appcompat-1.7.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,125,209,293,389,491,593,687", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "120,204,288,384,486,588,682,771"}, "to": {"startLines": "30,31,32,33,34,35,36,64", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1520,1590,1674,1758,1854,1956,2058,5104", "endColumns": "69,83,83,95,101,101,93,88", "endOffsets": "1585,1669,1753,1849,1951,2053,2147,5188"}}, {"source": "C:\\Users\\<USER>\\Music\\remote\\todolist\\app\\src\\main\\res\\values-night\\colors.xml", "from": {"startLines": "8,19,18,17,2,28,29,6,7,9,14,13,12,27,24,22,23,3", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "289,745,688,635,55,1079,1129,188,236,336,534,481,432,1031,940,844,891,97", "endColumns": "46,54,56,52,41,49,47,47,52,49,50,52,48,47,43,46,48,41", "endOffsets": "331,795,740,683,92,1124,1172,231,284,381,580,529,476,1074,979,886,935,134"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,102,157,214,267,309,359,407,455,508,558,609,662,711,759,803,850,899", "endColumns": "46,54,56,52,41,49,47,47,52,49,50,52,48,47,43,46,48,41", "endOffsets": "97,152,209,262,304,354,402,450,503,553,604,657,706,754,798,845,894,936"}}, {"source": "C:\\Users\\<USER>\\Desktop\\android_studio\\TheStream\\gradle\\caches\\8.9\\transforms\\e2a7f8f4879097be8d4279b82ba3ae42\\transformed\\material-1.12.0\\res\\values-night-v8\\values-night-v8.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,330,431,538,645,744,851,954,1081,1169,1293,1395,1497,1613,1715,1829,1957,2073,2195,2331,2451,2585,2705,2817,2943,3060,3184,3314,3436,3574,3708,3824", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "125,236,325,426,533,640,739,846,949,1076,1164,1288,1390,1492,1608,1710,1824,1952,2068,2190,2326,2446,2580,2700,2812,2938,3055,3179,3309,3431,3569,3703,3819,3939"}, "to": {"startLines": "37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,65,66,67,68,69,70,71,72", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2152,2227,2338,2427,2528,2635,2742,2841,2948,3051,3178,3266,3390,3492,3594,3710,3812,3926,4054,4170,4292,4428,4548,4682,4802,4914,5193,5310,5434,5564,5686,5824,5958,6074", "endColumns": "74,110,88,100,106,106,98,106,102,126,87,123,101,101,115,101,113,127,115,121,135,119,133,119,111,125,116,123,129,121,137,133,115,119", "endOffsets": "2222,2333,2422,2523,2630,2737,2836,2943,3046,3173,3261,3385,3487,3589,3705,3807,3921,4049,4165,4287,4423,4543,4677,4797,4909,5035,5305,5429,5559,5681,5819,5953,6069,6189"}}, {"source": "C:\\Users\\<USER>\\Music\\remote\\todolist\\app\\src\\main\\res\\values-night\\themes.xml", "from": {"startLines": "2,13", "startColumns": "4,4", "startOffsets": "113,718", "endLines": "11,13", "endColumns": "12,64", "endOffsets": "712,778"}, "to": {"startLines": "20,63", "startColumns": "4,4", "startOffsets": "941,5040", "endLines": "29,63", "endColumns": "12,63", "endOffsets": "1515,5099"}}]}]}