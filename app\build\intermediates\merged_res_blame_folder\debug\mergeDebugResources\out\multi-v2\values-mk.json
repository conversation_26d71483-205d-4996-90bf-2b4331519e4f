{"logs": [{"outputFile": "com.example.todolist.app-mergeDebugResources-30:/values-mk/values-mk.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fe0ea9fff5dbc0ebd57bd6ccf93c901b\\transformed\\appcompat-1.7.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,425,511,619,738,822,903,994,1087,1183,1277,1377,1470,1565,1661,1752,1843,1930,2036,2142,2243,2350,2462,2566,2722,2820", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "208,312,420,506,614,733,817,898,989,1082,1178,1272,1372,1465,1560,1656,1747,1838,1925,2031,2137,2238,2345,2457,2561,2717,2815,2903"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "321,429,533,641,727,835,954,1038,1119,1210,1303,1399,1493,1593,1686,1781,1877,1968,2059,2146,2252,2358,2459,2566,2678,2782,2938,9616", "endColumns": "107,103,107,85,107,118,83,80,90,92,95,93,99,92,94,95,90,90,86,105,105,100,106,111,103,155,97,87", "endOffsets": "424,528,636,722,830,949,1033,1114,1205,1298,1394,1488,1588,1681,1776,1872,1963,2054,2141,2247,2353,2454,2561,2673,2777,2933,3031,9699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b11727be95d45a60cc530c69ce7a2e71\\transformed\\core-1.13.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3465,3563,3665,3762,3860,3965,4068,9956", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "3558,3660,3757,3855,3960,4063,4179,10052"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e2a7f8f4879097be8d4279b82ba3ae42\\transformed\\material-1.12.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,271,352,432,514,611,700,796,920,1007,1070,1136,1227,1297,1361,1464,1527,1592,1652,1720,1783,1838,1966,2023,2085,2140,2215,2355,2442,2521,2614,2700,2783,2916,2998,3083,3229,3316,3393,3447,3502,3568,3641,3717,3788,3866,3939,4015,4090,4160,4269,4357,4432,4524,4616,4690,4764,4856,4909,4991,5058,5141,5228,5290,5354,5417,5487,5601,5716,5818,5930,5988,6047,6132,6221,6305", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,80,79,81,96,88,95,123,86,62,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84,88,83,78", "endOffsets": "266,347,427,509,606,695,791,915,1002,1065,1131,1222,1292,1356,1459,1522,1587,1647,1715,1778,1833,1961,2018,2080,2135,2210,2350,2437,2516,2609,2695,2778,2911,2993,3078,3224,3311,3388,3442,3497,3563,3636,3712,3783,3861,3934,4010,4085,4155,4264,4352,4427,4519,4611,4685,4759,4851,4904,4986,5053,5136,5223,5285,5349,5412,5482,5596,5711,5813,5925,5983,6042,6127,6216,6300,6379"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3036,3117,3197,3279,3376,4184,4280,4404,4491,4554,4620,4711,4781,4845,4948,5011,5076,5136,5204,5267,5322,5450,5507,5569,5624,5699,5839,5926,6005,6098,6184,6267,6400,6482,6567,6713,6800,6877,6931,6986,7052,7125,7201,7272,7350,7423,7499,7574,7644,7753,7841,7916,8008,8100,8174,8248,8340,8393,8475,8542,8625,8712,8774,8838,8901,8971,9085,9200,9302,9414,9472,9531,9704,9793,9877", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,80,79,81,96,88,95,123,86,62,65,90,69,63,102,62,64,59,67,62,54,127,56,61,54,74,139,86,78,92,85,82,132,81,84,145,86,76,53,54,65,72,75,70,77,72,75,74,69,108,87,74,91,91,73,73,91,52,81,66,82,86,61,63,62,69,113,114,101,111,57,58,84,88,83,78", "endOffsets": "316,3112,3192,3274,3371,3460,4275,4399,4486,4549,4615,4706,4776,4840,4943,5006,5071,5131,5199,5262,5317,5445,5502,5564,5619,5694,5834,5921,6000,6093,6179,6262,6395,6477,6562,6708,6795,6872,6926,6981,7047,7120,7196,7267,7345,7418,7494,7569,7639,7748,7836,7911,8003,8095,8169,8243,8335,8388,8470,8537,8620,8707,8769,8833,8896,8966,9080,9195,9297,9409,9467,9526,9611,9788,9872,9951"}}]}]}