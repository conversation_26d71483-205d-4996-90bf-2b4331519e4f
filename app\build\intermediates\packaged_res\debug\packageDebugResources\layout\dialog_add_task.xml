<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/add_new_task"
        android:textSize="18sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginBottom="16dp"
        android:gravity="center" />

    <EditText
        android:id="@+id/editTextTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:hint="@string/task_title"
        android:inputType="text"
        android:maxLines="2"
        android:minHeight="48dp"
        android:padding="12dp"
        android:background="@drawable/edit_text_background" />

    <EditText
        android:id="@+id/editTextDescription"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:hint="@string/task_description"
        android:inputType="textMultiLine"
        android:maxLines="4"
        android:minLines="2"
        android:minHeight="48dp"
        android:padding="12dp"
        android:background="@drawable/edit_text_background" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/task_status"
        android:textSize="14sp"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/spinnerStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:minHeight="48dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/task_priority"
        android:textSize="14sp"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="8dp" />

    <Spinner
        android:id="@+id/spinnerPriority"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:minHeight="48dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="end">

        <Button
            android:id="@+id/buttonCancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/cancel"
            android:layout_marginEnd="8dp"
            android:background="?android:attr/selectableItemBackground" />

        <Button
            android:id="@+id/buttonAdd"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/add_task_button" />

    </LinearLayout>

</LinearLayout>
