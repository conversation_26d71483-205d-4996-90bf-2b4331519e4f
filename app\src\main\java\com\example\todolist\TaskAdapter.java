package com.example.todolist;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;
import java.util.ArrayList;
import java.util.List;

public class TaskAdapter extends BaseAdapter {
    private Context context;
    private List<Task> tasks;
    private List<Task> filteredTasks;
    private LayoutInflater inflater;
    
    public TaskAdapter(Context context, List<Task> tasks) {
        this.context = context;
        this.tasks = tasks;
        this.filteredTasks = new ArrayList<>(tasks);
        this.inflater = LayoutInflater.from(context);
    }
    
    @Override
    public int getCount() {
        return filteredTasks.size();
    }
    
    @Override
    public Object getItem(int position) {
        return filteredTasks.get(position);
    }
    
    @Override
    public long getItemId(int position) {
        return position;
    }
    
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.task_item, parent, false);
            holder = new ViewHolder();
            holder.titleTextView = convertView.findViewById(R.id.taskTitle);
            holder.descriptionTextView = convertView.findViewById(R.id.taskDescription);
            holder.statusTextView = convertView.findViewById(R.id.taskStatus);
            holder.priorityTextView = convertView.findViewById(R.id.taskPriority);
            holder.priorityIndicator = convertView.findViewById(R.id.priorityIndicator);
            holder.dateTextView = convertView.findViewById(R.id.taskDate);
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        
        Task task = filteredTasks.get(position);
        
        // Set task data
        holder.titleTextView.setText(task.getTitle());
        holder.descriptionTextView.setText(task.getDescription());
        holder.statusTextView.setText(task.getStatus());
        holder.priorityTextView.setText(task.getPriority());
        holder.dateTextView.setText(task.getFormattedDateCreated());

        // Set status color
        setStatusColor(holder.statusTextView, task.getStatus());

        // Set priority color
        setPriorityColor(holder.priorityTextView, holder.priorityIndicator, task.getPriority());

        // Set background color based on status
        setItemBackgroundColor(convertView, task.getStatus());
        
        return convertView;
    }
    
    private void setStatusColor(TextView statusTextView, String status) {
        switch (status) {
            case Task.STATUS_PENDING:
                statusTextView.setTextColor(Color.parseColor("#FF9800")); // Orange
                break;
            case Task.STATUS_IN_PROGRESS:
                statusTextView.setTextColor(Color.parseColor("#2196F3")); // Blue
                break;
            case Task.STATUS_COMPLETED:
                statusTextView.setTextColor(Color.parseColor("#4CAF50")); // Green
                break;
            default:
                statusTextView.setTextColor(Color.GRAY);
                break;
        }
    }
    
    private void setPriorityColor(TextView priorityTextView, View priorityIndicator, String priority) {
        int color;
        switch (priority) {
            case Task.PRIORITY_HIGH:
                color = Color.parseColor("#F44336"); // Red
                break;
            case Task.PRIORITY_MEDIUM:
                color = Color.parseColor("#FF9800"); // Orange
                break;
            case Task.PRIORITY_LOW:
                color = Color.parseColor("#4CAF50"); // Green
                break;
            default:
                color = Color.parseColor("#9E9E9E"); // Gray
                break;
        }
        priorityTextView.setBackgroundColor(color);
        priorityIndicator.setBackgroundColor(color);
    }

    private void setItemBackgroundColor(View itemView, String status) {
        switch (status) {
            case Task.STATUS_PENDING:
                itemView.setBackgroundColor(Color.parseColor("#FFF3E0")); // Light Orange
                break;
            case Task.STATUS_IN_PROGRESS:
                itemView.setBackgroundColor(Color.parseColor("#E3F2FD")); // Light Blue
                break;
            case Task.STATUS_COMPLETED:
                itemView.setBackgroundColor(Color.parseColor("#E8F5E8")); // Light Green
                break;
            default:
                itemView.setBackgroundColor(Color.WHITE);
                break;
        }
    }
    
    // Filter methods
    public void filterByStatus(String status) {
        filteredTasks.clear();
        if (status == null || status.equals("Hamısı")) {
            filteredTasks.addAll(tasks);
        } else {
            for (Task task : tasks) {
                if (task.getStatus().equals(status)) {
                    filteredTasks.add(task);
                }
            }
        }
        notifyDataSetChanged();
    }
    
    public void filterByTitle(String searchText) {
        filteredTasks.clear();
        if (searchText == null || searchText.isEmpty()) {
            filteredTasks.addAll(tasks);
        } else {
            String searchLower = searchText.toLowerCase();
            for (Task task : tasks) {
                if (task.getTitle().toLowerCase().contains(searchLower) ||
                    task.getDescription().toLowerCase().contains(searchLower)) {
                    filteredTasks.add(task);
                }
            }
        }
        notifyDataSetChanged();
    }
    
    public void resetFilter() {
        filteredTasks.clear();
        filteredTasks.addAll(tasks);
        notifyDataSetChanged();
    }
    
    public Task getTaskAt(int position) {
        if (position >= 0 && position < filteredTasks.size()) {
            return filteredTasks.get(position);
        }
        return null;
    }
    
    public int getOriginalPosition(Task task) {
        return tasks.indexOf(task);
    }
    
    static class ViewHolder {
        TextView titleTextView;
        TextView descriptionTextView;
        TextView statusTextView;
        TextView priorityTextView;
        View priorityIndicator;
        TextView dateTextView;
    }
}
