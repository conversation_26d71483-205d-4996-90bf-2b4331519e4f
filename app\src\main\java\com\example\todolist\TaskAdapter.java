package com.example.todolist;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;
import android.widget.CheckBox;
import android.widget.Toast;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import android.widget.CompoundButton; // New import
import android.view.animation.AnimationUtils; // New import
import android.view.animation.Animation; // New import

public class TaskAdapter extends BaseAdapter {
    private Context context;
    private List<Task> tasks;
    private List<Task> filteredTasks;
    private LayoutInflater inflater;
    private OnTaskActionListener listener; // New listener interface

    // Interface for task actions
    public interface OnTaskActionListener {
        void onTaskStatusChanged(Task task, boolean isCompleted);
        void onTaskEdit(Task task);
        void onTaskDelete(Task task);
    }
    
    public TaskAdapter(Context context, List<Task> tasks, OnTaskActionListener listener) {
        this.context = context;
        this.tasks = tasks;
        this.filteredTasks = new ArrayList<>(tasks);
        this.inflater = LayoutInflater.from(context);
        this.listener = listener;
    }
    
    @Override
    public int getCount() {
        return filteredTasks.size();
    }
    
    @Override
    public Object getItem(int position) {
        return filteredTasks.get(position);
    }
    
    @Override
    public long getItemId(int position) {
        return position;
    }
    
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        
        if (convertView == null) {
            convertView = inflater.inflate(R.layout.task_item, parent, false);
            holder = new ViewHolder();
            holder.titleTextView = convertView.findViewById(R.id.taskTitle);
            holder.descriptionTextView = convertView.findViewById(R.id.taskDescription);
            holder.statusTextView = convertView.findViewById(R.id.taskStatus);
            holder.priorityTextView = convertView.findViewById(R.id.taskPriority);
            holder.priorityIndicator = convertView.findViewById(R.id.priorityIndicator);
            holder.dateTextView = convertView.findViewById(R.id.taskDate);
            holder.deadlineTextView = convertView.findViewById(R.id.taskDeadline);
            holder.completedCheckbox = convertView.findViewById(R.id.taskCompletedCheckbox);
            holder.reminderLayout = convertView.findViewById(R.id.reminderLayout); // New
            holder.taskReminderTime = convertView.findViewById(R.id.taskReminderTime); // New
            convertView.setTag(holder);
        } else {
            holder = (ViewHolder) convertView.getTag();
        }
        
        Task task = filteredTasks.get(position);

        // Set task data with null checks
        holder.titleTextView.setText(task.getTitle() != null ? task.getTitle() : "");
        holder.descriptionTextView.setText(task.getDescription() != null ? task.getDescription() : "");
        holder.statusTextView.setText(task.getStatus() != null ? task.getStatus() : Task.STATUS_PENDING);
        holder.priorityTextView.setText(task.getPriority() != null ? task.getPriority() : Task.PRIORITY_MEDIUM);
        holder.dateTextView.setText(task.getFormattedDateCreated());
        holder.deadlineTextView.setText(task.getFormattedDeadline());

        // Set reminder time and visibility
        if (task.hasReminder()) {
            holder.taskReminderTime.setText(context.getString(R.string.task_reminder) + ": " + task.getFormattedReminderTime());
            holder.reminderLayout.setVisibility(View.VISIBLE);
        } else {
            holder.reminderLayout.setVisibility(View.GONE);
        }

        // Set checkbox state and listener
        holder.completedCheckbox.setOnCheckedChangeListener(null); // Remove previous listener to prevent unwanted triggers
        holder.completedCheckbox.setChecked(task.isCompleted());
        holder.completedCheckbox.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (listener != null) {
                listener.onTaskStatusChanged(task, isChecked);
            }
        });

        // Set click listener for editing
        convertView.setOnClickListener(v -> {
            if (listener != null) {
                listener.onTaskEdit(task);
            }
        });

        // Set long click listener for deleting
        convertView.setOnLongClickListener(v -> {
            if (listener != null) {
                listener.onTaskDelete(task);
                return true; // Consume the long click
            }
            return false;
        });

        // Set status color
        setStatusColor(holder.statusTextView, task.getStatus());

        // Set priority color
        setPriorityColor(holder.priorityTextView, holder.priorityIndicator, task.getPriority());

        // Set background color based on status
        setItemBackgroundColor(convertView, task.getStatus());
        
        return convertView;
    }
    
    private void setStatusColor(TextView statusTextView, String status) {
        switch (status) {
            case Task.STATUS_PENDING:
                statusTextView.setTextColor(context.getResources().getColor(R.color.neutral_700)); // Darker color for better contrast
                break;
            case Task.STATUS_IN_PROGRESS:
                statusTextView.setTextColor(context.getResources().getColor(R.color.primary_600)); // Blue from colors.xml
                break;
            case Task.STATUS_COMPLETED:
                statusTextView.setTextColor(context.getResources().getColor(R.color.success_600)); // Green from colors.xml
                break;
            default:
                statusTextView.setTextColor(Color.GRAY);
                break;
        }
    }
    
    private void setPriorityColor(TextView priorityTextView, View priorityIndicator, String priority) {
        int color;
        switch (priority) {
            case Task.PRIORITY_HIGH:
                color = context.getResources().getColor(R.color.error_600); // Red from colors.xml
                break;
            case Task.PRIORITY_MEDIUM:
                color = context.getResources().getColor(R.color.warning_600); // Orange from colors.xml
                break;
            case Task.PRIORITY_LOW:
                color = context.getResources().getColor(R.color.success_600); // Green from colors.xml
                break;
            default:
                color = context.getResources().getColor(R.color.neutral_500); // Gray from colors.xml
                break;
        }
        priorityTextView.setBackgroundColor(color);
        priorityIndicator.setBackgroundColor(color);
    }

    private void setItemBackgroundColor(View itemView, String status) {
        int backgroundColor;
        if (Task.STATUS_COMPLETED.equals(status)) {
            backgroundColor = context.getResources().getColor(R.color.success_50); // Very light green for completed tasks
        } else {
            switch (status) {
                case Task.STATUS_PENDING:
                    backgroundColor = context.getResources().getColor(R.color.neutral_50); // Light neutral for pending
                    break;
                case Task.STATUS_IN_PROGRESS:
                    backgroundColor = context.getResources().getColor(R.color.primary_50); // Light blue for in progress
                    break;
                default:
                    backgroundColor = context.getResources().getColor(R.color.neutral_0); // Default to white
                    break;
            }
        }
        itemView.setBackgroundColor(backgroundColor);
    }
    
    // Filter methods
    public void filterByStatus(String status) {
        filteredTasks.clear();
        if (status == null || status.equals("Hamısı")) {
            filteredTasks.addAll(tasks);
        } else {
            for (Task task : tasks) {
                if (task.getStatus().equals(status)) {
                    filteredTasks.add(task);
                }
            }
        }
        notifyDataSetChanged();
    }
    
    public void filterByTitle(String searchText) {
        filteredTasks.clear();
        if (searchText == null || searchText.isEmpty()) {
            filteredTasks.addAll(tasks);
        } else {
            String searchLower = searchText.toLowerCase();
            for (Task task : tasks) {
                if (task.getTitle().toLowerCase().contains(searchLower) ||
                    task.getDescription().toLowerCase().contains(searchLower)) {
                    filteredTasks.add(task);
                }
            }
        }
        notifyDataSetChanged();
    }
    
    public void resetFilter() {
        filteredTasks.clear();
        filteredTasks.addAll(tasks);
        notifyDataSetChanged();
    }

    public void filterByStatusExcludingCompleted() {
        filteredTasks.clear();
        for (Task task : tasks) {
            if (!task.getStatus().equals(Task.STATUS_COMPLETED)) {
                filteredTasks.add(task);
            }
        }
        notifyDataSetChanged();
    }
    
    public Task getTaskAt(int position) {
        if (position >= 0 && position < filteredTasks.size()) {
            return filteredTasks.get(position);
        }
        return null;
    }
    
    public int getOriginalPosition(Task task) {
        return tasks.indexOf(task);
    }

    // New methods for managing tasks
    public void addTask(Task task) {
        tasks.add(task);
        filteredTasks.add(task); // Also add to filtered list for immediate display
        notifyDataSetChanged();
    }

    public void updateTask(Task oldTask, Task newTask) {
        int indexInTasks = tasks.indexOf(oldTask);
        if (indexInTasks != -1) {
            tasks.set(indexInTasks, newTask);
        }
        int indexInFiltered = filteredTasks.indexOf(oldTask);
        if (indexInFiltered != -1) {
            filteredTasks.set(indexInFiltered, newTask);
        }
        notifyDataSetChanged();
    }

    public void removeTask(Task task) {
        tasks.remove(task);
        filteredTasks.remove(task);
        notifyDataSetChanged();
    }
    
    static class ViewHolder {
        TextView titleTextView;
        TextView descriptionTextView;
        TextView statusTextView;
        TextView priorityTextView;
        View priorityIndicator;
        TextView dateTextView;
        TextView deadlineTextView;
        CheckBox completedCheckbox;
        LinearLayout reminderLayout; // New
        TextView taskReminderTime; // New
    }

    // Sorting methods
    public void sortByDateCreated() {
        // Sort in descending order so latest created appears first
        Collections.sort(filteredTasks, (t1, t2) -> Long.compare(t2.getDateCreated(), t1.getDateCreated()));
        notifyDataSetChanged();
    }

    public void sortByPriority() {
        Collections.sort(filteredTasks, (t1, t2) -> Integer.compare(t2.getPriorityLevel(), t1.getPriorityLevel()));
        notifyDataSetChanged();
    }

    public void sortByStatus() {
        Collections.sort(filteredTasks, (t1, t2) -> t1.getStatus().compareTo(t2.getStatus()));
        notifyDataSetChanged();
    }

    public void sortByTitle() {
        Collections.sort(filteredTasks, (t1, t2) -> t1.getTitle().compareToIgnoreCase(t2.getTitle()));
        notifyDataSetChanged();
    }

    public void sortByDeadline() {
        Collections.sort(filteredTasks, (t1, t2) -> {
            if (t1.getDeadline() == 0 && t2.getDeadline() == 0) return 0;
            if (t1.getDeadline() == 0) return 1; // Tasks without deadline go to the end
            if (t2.getDeadline() == 0) return -1; // Tasks without deadline go to the end
            return Long.compare(t1.getDeadline(), t2.getDeadline());
        });
        notifyDataSetChanged();
    }

    public void sortByCategory() {
        Collections.sort(filteredTasks, (t1, t2) -> t1.getCategory().compareToIgnoreCase(t2.getCategory()));
        notifyDataSetChanged();
    }
}
