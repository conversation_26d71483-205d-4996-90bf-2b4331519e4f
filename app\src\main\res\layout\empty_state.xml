<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp"
    android:background="@color/surface_color">

    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:src="@drawable/ic_empty_tasks"
        android:tint="@color/text_hint"
        android:layout_marginBottom="24dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_tasks_title"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@color/text_primary"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_tasks_subtitle"
        android:textSize="14sp"
        android:textColor="@color/text_secondary"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <Button
        android:id="@+id/buttonAddFirstTask"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/add_first_task"
        android:backgroundTint="@color/accent_color"
        android:textColor="@android:color/white"
        android:paddingHorizontal="24dp" />

</LinearLayout>
