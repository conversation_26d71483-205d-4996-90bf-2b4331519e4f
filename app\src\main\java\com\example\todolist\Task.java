package com.example.todolist;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class Task {
    private String title;
    private String description;
    private String status;
    private String priority;
    private String category;
    private long dateCreated;
    private long dateModified;
    private long deadline;
    private long reminderTime; // New field for reminder time
    
    // Status constants
    public static final String STATUS_PENDING = "Gözləyir";
    public static final String STATUS_IN_PROGRESS = "Davam Edir";
    public static final String STATUS_COMPLETED = "Tamamlandı";

    // Priority constants
    public static final String PRIORITY_HIGH = "Yüksək";
    public static final String PRIORITY_MEDIUM = "Orta";
    public static final String PRIORITY_LOW = "Aşağı";

    // Category constants
    public static final String CATEGORY_WORK = "İş";
    public static final String CATEGORY_PERSONAL = "Şəxsi";
    public static final String CATEGORY_SHOPPING = "Alış-veriş";
    public static final String CATEGORY_HEALTH = "Sağlamlıq";
    public static final String CATEGORY_EDUCATION = "Təhsil";
    public static final String CATEGORY_OTHER = "Digər";
    
    // Constructors
    public Task() {
        this.dateCreated = System.currentTimeMillis();
        this.dateModified = System.currentTimeMillis();
        this.status = STATUS_PENDING;
        this.priority = PRIORITY_MEDIUM;
        this.category = CATEGORY_OTHER;
    }

    public Task(String title, String description) {
        this();
        this.title = title;
        this.description = description;
    }

    public Task(String title, String description, String status) {
        this(title, description);
        this.status = status;
    }

    public Task(String title, String description, String status, String priority) {
        this(title, description, status);
        this.priority = priority;
    }

    public Task(String title, String description, String status, String priority, long deadline) {
        this(title, description, status, priority);
        this.deadline = deadline;
        this.reminderTime = 0; // Initialize reminder time
    }

    public Task(String title, String description, String status, String priority, long deadline, long reminderTime) {
        this(title, description, status, priority, deadline);
        this.reminderTime = reminderTime;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
        this.dateModified = System.currentTimeMillis();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.dateModified = System.currentTimeMillis();
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
        this.dateModified = System.currentTimeMillis();
    }
    
    public long getDateCreated() {
        return dateCreated;
    }
    
    public void setDateCreated(long dateCreated) {
        this.dateCreated = dateCreated;
    }
    
    public long getDateModified() {
        return dateModified;
    }
    
    public void setDateModified(long dateModified) {
        this.dateModified = dateModified;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
        this.dateModified = System.currentTimeMillis();
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
        this.dateModified = System.currentTimeMillis();
    }

    public long getDeadline() {
        return deadline;
    }

    public void setDeadline(long deadline) {
        this.deadline = deadline;
        this.dateModified = System.currentTimeMillis();
    }

    public String getFormattedDeadline() {
        if (deadline == 0) return "Deadline yoxdur";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
            return sdf.format(new Date(deadline));
        } catch (Exception e) {
            return "Deadline yoxdur";
        }
    }

    public boolean hasDeadline() {
        return deadline > 0;
    }

    public boolean isOverdue() {
        return hasDeadline() && deadline < System.currentTimeMillis() && !isCompleted();
    }

    public boolean isDueSoon() {
        if (!hasDeadline() || isCompleted()) return false;
        long oneDayInMillis = 24 * 60 * 60 * 1000;
        return deadline - System.currentTimeMillis() <= oneDayInMillis;
    }

    public long getReminderTime() {
        return reminderTime;
    }

    public void setReminderTime(long reminderTime) {
        this.reminderTime = reminderTime;
        this.dateModified = System.currentTimeMillis();
    }

    public String getFormattedReminderTime() {
        if (reminderTime == 0) return "Xatırlatma yoxdur";
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
            return sdf.format(new Date(reminderTime));
        } catch (Exception e) {
            return "Xatırlatma yoxdur";
        }
    }

    public boolean hasReminder() {
        return reminderTime > 0;
    }
    
    // Utility methods
    public String getFormattedDateCreated() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
        return sdf.format(new Date(dateCreated));
    } // Added missing brace
    
    public String getFormattedDateModified() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
        return sdf.format(new Date(dateModified));
    }
    
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(status);
    }
    
    public boolean isPending() {
        return STATUS_PENDING.equals(status);
    }
    
    public boolean isInProgress() {
        return STATUS_IN_PROGRESS.equals(status);
    }

    public boolean isHighPriority() {
        return PRIORITY_HIGH.equals(priority);
    }

    public boolean isMediumPriority() {
        return PRIORITY_MEDIUM.equals(priority);
    }

    public boolean isLowPriority() {
        return PRIORITY_LOW.equals(priority);
    }

    public int getPriorityLevel() {
        switch (priority) {
            case PRIORITY_HIGH: return 3;
            case PRIORITY_MEDIUM: return 2;
            case PRIORITY_LOW: return 1;
            default: return 2;
        }
    }
    
    @Override
    public String toString() {
        return title + " (" + status + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Task task = (Task) obj;
        return dateCreated == task.dateCreated &&
               title != null ? title.equals(task.title) : task.title == null;
    }
    
    @Override
    public int hashCode() {
        int result = title != null ? title.hashCode() : 0;
        result = 31 * result + (int) (dateCreated ^ (dateCreated >>> 32));
        return result;
    }
}
