package com.example.todolist;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class Task {
    private String title;
    private String description;
    private String status;
    private long dateCreated;
    private long dateModified;
    
    // Status constants
    public static final String STATUS_PENDING = "<PERSON><PERSON><PERSON><PERSON>əyir";
    public static final String STATUS_IN_PROGRESS = "Davam Edir";
    public static final String STATUS_COMPLETED = "Tamamlandı";
    
    // Constructors
    public Task() {
        this.dateCreated = System.currentTimeMillis();
        this.dateModified = System.currentTimeMillis();
        this.status = STATUS_PENDING;
    }
    
    public Task(String title, String description) {
        this();
        this.title = title;
        this.description = description;
    }
    
    public Task(String title, String description, String status) {
        this(title, description);
        this.status = status;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
        this.dateModified = System.currentTimeMillis();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.dateModified = System.currentTimeMillis();
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
        this.dateModified = System.currentTimeMillis();
    }
    
    public long getDateCreated() {
        return dateCreated;
    }
    
    public void setDateCreated(long dateCreated) {
        this.dateCreated = dateCreated;
    }
    
    public long getDateModified() {
        return dateModified;
    }
    
    public void setDateModified(long dateModified) {
        this.dateModified = dateModified;
    }
    
    // Utility methods
    public String getFormattedDateCreated() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
        return sdf.format(new Date(dateCreated));
    }
    
    public String getFormattedDateModified() {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm", Locale.getDefault());
        return sdf.format(new Date(dateModified));
    }
    
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(status);
    }
    
    public boolean isPending() {
        return STATUS_PENDING.equals(status);
    }
    
    public boolean isInProgress() {
        return STATUS_IN_PROGRESS.equals(status);
    }
    
    @Override
    public String toString() {
        return title + " (" + status + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Task task = (Task) obj;
        return dateCreated == task.dateCreated &&
               title != null ? title.equals(task.title) : task.title == null;
    }
    
    @Override
    public int hashCode() {
        int result = title != null ? title.hashCode() : 0;
        result = 31 * result + (int) (dateCreated ^ (dateCreated >>> 32));
        return result;
    }
}
