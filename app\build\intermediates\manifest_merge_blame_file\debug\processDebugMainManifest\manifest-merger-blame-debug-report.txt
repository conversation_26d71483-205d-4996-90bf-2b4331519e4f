1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.todolist"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
11-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:5:5-77
11-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:5:22-74
12
13    <permission
13-->[androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b11727be95d45a60cc530c69ce7a2e71\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
14        android:name="com.example.todolist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
14-->[androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b11727be95d45a60cc530c69ce7a2e71\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
15        android:protectionLevel="signature" />
15-->[androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b11727be95d45a60cc530c69ce7a2e71\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
16
17    <uses-permission android:name="com.example.todolist.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b11727be95d45a60cc530c69ce7a2e71\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
17-->[androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b11727be95d45a60cc530c69ce7a2e71\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
18
19    <application
19-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:7:5-27:19
20        android:allowBackup="true"
20-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:8:9-35
21        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
21-->[androidx.core:core:1.13.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b11727be95d45a60cc530c69ce7a2e71\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
22        android:debuggable="true"
23        android:extractNativeLibs="true"
24        android:icon="@mipmap/ic_launcher"
24-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:9:9-43
25        android:label="@string/app_name"
25-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:10:9-41
26        android:supportsRtl="true"
26-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:11:9-35
27        android:testOnly="true"
28        android:theme="@style/Theme.Todolist" >
28-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:12:9-46
29        <activity
29-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:13:9-21:20
30            android:name="com.example.todolist.MainActivity"
30-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:14:13-41
31            android:exported="true" >
31-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:15:13-36
32            <intent-filter>
32-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:16:13-20:29
33                <action android:name="android.intent.action.MAIN" />
33-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:17:17-69
33-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:17:25-66
34
35                <category android:name="android.intent.category.LAUNCHER" />
35-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:19:17-77
35-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:19:27-74
36            </intent-filter>
37        </activity>
38        <activity
38-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:23:9-26:58
39            android:name="com.example.todolist.StatisticsActivity"
39-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:24:13-47
40            android:exported="false"
40-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:25:13-37
41            android:parentActivityName="com.example.todolist.MainActivity" />
41-->C:\Users\<USER>\Music\remote\todolist\app\src\main\AndroidManifest.xml:26:13-55
42
43        <provider
43-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\49db857962f75b674f129af70793bf92\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
44            android:name="androidx.startup.InitializationProvider"
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\49db857962f75b674f129af70793bf92\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
45            android:authorities="com.example.todolist.androidx-startup"
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\49db857962f75b674f129af70793bf92\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
46            android:exported="false" >
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\49db857962f75b674f129af70793bf92\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
47            <meta-data
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\49db857962f75b674f129af70793bf92\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
48                android:name="androidx.emoji2.text.EmojiCompatInitializer"
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\49db857962f75b674f129af70793bf92\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
49                android:value="androidx.startup" />
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\49db857962f75b674f129af70793bf92\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
50            <meta-data
50-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a7566ef00b897f386246b210a3a7334c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
51                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
51-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a7566ef00b897f386246b210a3a7334c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
52                android:value="androidx.startup" />
52-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\a7566ef00b897f386246b210a3a7334c\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
53            <meta-data
53-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
54                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
54-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
55                android:value="androidx.startup" />
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
56        </provider>
57
58        <receiver
58-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
59            android:name="androidx.profileinstaller.ProfileInstallReceiver"
59-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
60            android:directBootAware="false"
60-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
61            android:enabled="true"
61-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
62            android:exported="true"
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
63            android:permission="android.permission.DUMP" >
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
64            <intent-filter>
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
65                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
66            </intent-filter>
67            <intent-filter>
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
68                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
69            </intent-filter>
70            <intent-filter>
70-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
71                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
72            </intent-filter>
73            <intent-filter>
73-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
74                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\Desktop\android_studio\TheStream\gradle\caches\8.9\transforms\b7217aea117a0e3619c8e894c64b752a\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
75            </intent-filter>
76        </receiver>
77    </application>
78
79</manifest>
