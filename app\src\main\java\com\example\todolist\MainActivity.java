package com.example.todolist;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import java.util.ArrayList;
import java.util.Arrays;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.google.android.material.floatingactionbutton.FloatingActionButton;

public class MainActivity extends AppCompatActivity {

    private Button filterButton;
    private FloatingActionButton fabAddTask;
    private ListView tasksListView;
    private TextView tasksCountTextView;
    private SwipeRefreshLayout swipeRefreshLayout;
    private View emptyStateView;
    private ArrayList<Task> tasks;
    private TaskAdapter adapter;
    private String currentFilter = "Hamısı";
    private String currentSearchText = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initializeViews();
        setupTasksList();
        setupClickListeners();
        setupNotifications();
        checkOverdueTasks();
    }

    private void initializeViews() {
        fabAddTask = findViewById(R.id.fabAddTask);
        filterButton = findViewById(R.id.filterButton);
        tasksListView = findViewById(R.id.tasksListView);
        tasksCountTextView = findViewById(R.id.tasksCountTextView);
        swipeRefreshLayout = findViewById(R.id.swipeRefreshLayout);
        emptyStateView = findViewById(R.id.emptyStateView);

        // Setup empty state button
        Button addFirstTaskButton = emptyStateView.findViewById(R.id.buttonAddFirstTask);
        addFirstTaskButton.setOnClickListener(v -> showAddTaskDialog());
    }

    private void setupTasksList() {
        tasks = new ArrayList<>();
        loadTasks();
        adapter = new TaskAdapter(this, tasks);
        tasksListView.setAdapter(adapter);
        updateTasksCount();

        // Setup SwipeRefreshLayout
        swipeRefreshLayout.setColorSchemeResources(
                R.color.primary_color,
                R.color.accent_color,
                R.color.secondary_color
        );
        swipeRefreshLayout.setOnRefreshListener(this::refreshTasks);
    }

    private void setupClickListeners() {
        // FAB click listener with animation
        fabAddTask.setOnClickListener(v -> {
            Animation scaleIn = AnimationUtils.loadAnimation(this, R.anim.scale_in);
            v.startAnimation(scaleIn);
            showAddTaskDialog();
        });

        // Filter button click listener
        filterButton.setOnClickListener(v -> showFilterDialog());

        // Task item click listener for editing
        tasksListView.setOnItemClickListener((parent, view, position, id) -> {
            Task task = adapter.getTaskAt(position);
            if (task != null) {
                showEditTaskDialog(task);
            }
        });

        // Task item long click listener for deleting
        tasksListView.setOnItemLongClickListener((parent, view, position, id) -> {
            Task task = adapter.getTaskAt(position);
            if (task != null) {
                showDeleteTaskDialog(task);
            }
            return true;
        });
    }

    private void showAddTaskDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_add_task, null);
        builder.setView(dialogView);

        EditText titleEditText = dialogView.findViewById(R.id.editTextTitle);
        EditText descriptionEditText = dialogView.findViewById(R.id.editTextDescription);
        Spinner statusSpinner = dialogView.findViewById(R.id.spinnerStatus);
        Spinner prioritySpinner = dialogView.findViewById(R.id.spinnerPriority);
        Button addButton = dialogView.findViewById(R.id.buttonAdd);
        Button cancelButton = dialogView.findViewById(R.id.buttonCancel);

        // Setup status spinner
        String[] statusOptions = {Task.STATUS_PENDING, Task.STATUS_IN_PROGRESS, Task.STATUS_COMPLETED};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, statusOptions);
        statusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(statusAdapter);

        // Setup priority spinner
        String[] priorityOptions = {Task.PRIORITY_HIGH, Task.PRIORITY_MEDIUM, Task.PRIORITY_LOW};
        ArrayAdapter<String> priorityAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, priorityOptions);
        priorityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        prioritySpinner.setAdapter(priorityAdapter);
        prioritySpinner.setSelection(1); // Default to medium priority

        AlertDialog dialog = builder.create();

        addButton.setOnClickListener(v -> {
            String title = titleEditText.getText().toString().trim();
            String description = descriptionEditText.getText().toString().trim();
            String status = statusSpinner.getSelectedItem().toString();
            String priority = prioritySpinner.getSelectedItem().toString();

            if (title.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_title), Toast.LENGTH_SHORT).show();
                return;
            }

            if (description.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_description), Toast.LENGTH_SHORT).show();
                return;
            }

            Task newTask = new Task(title, description, status, priority);
            tasks.add(newTask);

            // Animate new task addition
            Animation slideIn = AnimationUtils.loadAnimation(this, R.anim.slide_in_right);
            tasksListView.startAnimation(slideIn);

            adapter.notifyDataSetChanged();
            saveTasks();
            updateTasksCount();
            Toast.makeText(this, getString(R.string.task_added), Toast.LENGTH_SHORT).show();
            dialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> dialog.dismiss());
        dialog.show();
    }

    private void showEditTaskDialog(Task task) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_add_task, null);
        builder.setView(dialogView);

        EditText titleEditText = dialogView.findViewById(R.id.editTextTitle);
        EditText descriptionEditText = dialogView.findViewById(R.id.editTextDescription);
        Spinner statusSpinner = dialogView.findViewById(R.id.spinnerStatus);
        Spinner prioritySpinner = dialogView.findViewById(R.id.spinnerPriority);
        Button addButton = dialogView.findViewById(R.id.buttonAdd);
        Button cancelButton = dialogView.findViewById(R.id.buttonCancel);

        // Pre-fill with existing task data
        titleEditText.setText(task.getTitle());
        descriptionEditText.setText(task.getDescription());

        // Setup status spinner
        String[] statusOptions = {Task.STATUS_PENDING, Task.STATUS_IN_PROGRESS, Task.STATUS_COMPLETED};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, statusOptions);
        statusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(statusAdapter);

        // Setup priority spinner
        String[] priorityOptions = {Task.PRIORITY_HIGH, Task.PRIORITY_MEDIUM, Task.PRIORITY_LOW};
        ArrayAdapter<String> priorityAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, priorityOptions);
        priorityAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        prioritySpinner.setAdapter(priorityAdapter);

        // Set current status
        int statusPosition = Arrays.asList(statusOptions).indexOf(task.getStatus());
        if (statusPosition >= 0) {
            statusSpinner.setSelection(statusPosition);
        }

        // Set current priority
        int priorityPosition = Arrays.asList(priorityOptions).indexOf(task.getPriority());
        if (priorityPosition >= 0) {
            prioritySpinner.setSelection(priorityPosition);
        }

        addButton.setText(getString(R.string.edit_task));
        AlertDialog dialog = builder.create();

        addButton.setOnClickListener(v -> {
            String title = titleEditText.getText().toString().trim();
            String description = descriptionEditText.getText().toString().trim();
            String status = statusSpinner.getSelectedItem().toString();
            String priority = prioritySpinner.getSelectedItem().toString();

            if (title.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_title), Toast.LENGTH_SHORT).show();
                return;
            }

            if (description.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_description), Toast.LENGTH_SHORT).show();
                return;
            }

            task.setTitle(title);
            task.setDescription(description);
            task.setStatus(status);
            task.setPriority(priority);
            adapter.notifyDataSetChanged();
            saveTasks();
            Toast.makeText(this, getString(R.string.task_updated), Toast.LENGTH_SHORT).show();
            dialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> dialog.dismiss());
        dialog.show();
    }

    private void showDeleteTaskDialog(Task task) {
        new AlertDialog.Builder(this)
                .setTitle(getString(R.string.delete_task))
                .setMessage(getString(R.string.delete_confirmation))
                .setPositiveButton(getString(R.string.yes), (dialog, which) -> {
                    int originalPosition = adapter.getOriginalPosition(task);
                    if (originalPosition >= 0) {
                        // Animate task removal
                        Animation slideOut = AnimationUtils.loadAnimation(this, R.anim.slide_out_left);
                        tasksListView.startAnimation(slideOut);

                        tasks.remove(originalPosition);
                        adapter.notifyDataSetChanged();
                        saveTasks();
                        updateTasksCount();
                        Toast.makeText(this, getString(R.string.task_deleted), Toast.LENGTH_SHORT).show();
                    }
                })
                .setNegativeButton(getString(R.string.no), null)
                .show();
    }

    private void showFilterDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_filter, null);
        builder.setView(dialogView);

        Spinner statusSpinner = dialogView.findViewById(R.id.spinnerFilterStatus);
        EditText searchEditText = dialogView.findViewById(R.id.editTextSearch);
        Button applyButton = dialogView.findViewById(R.id.buttonApplyFilter);
        Button clearButton = dialogView.findViewById(R.id.buttonClearFilter);

        // Setup status spinner
        String[] filterOptions = {getString(R.string.status_all), Task.STATUS_PENDING, Task.STATUS_IN_PROGRESS, Task.STATUS_COMPLETED};
        ArrayAdapter<String> filterAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, filterOptions);
        filterAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(filterAdapter);

        // Set current filter
        int filterPosition = Arrays.asList(filterOptions).indexOf(currentFilter);
        if (filterPosition >= 0) {
            statusSpinner.setSelection(filterPosition);
        }

        // Set current search text
        searchEditText.setText(currentSearchText);

        AlertDialog dialog = builder.create();

        applyButton.setOnClickListener(v -> {
            currentFilter = statusSpinner.getSelectedItem().toString();
            currentSearchText = searchEditText.getText().toString().trim();
            applyFilters();
            dialog.dismiss();
        });

        clearButton.setOnClickListener(v -> {
            currentFilter = getString(R.string.status_all);
            currentSearchText = "";
            adapter.resetFilter();
            updateTasksCount();
            dialog.dismiss();
        });

        dialog.show();
    }

    private void applyFilters() {
        if (!currentSearchText.isEmpty()) {
            adapter.filterByTitle(currentSearchText);
        } else if (!currentFilter.equals(getString(R.string.status_all))) {
            adapter.filterByStatus(currentFilter);
        } else {
            adapter.resetFilter();
        }
        updateTasksCount();
    }

    private void updateTasksCount() {
        int totalTasks = tasks.size();
        tasksCountTextView.setText(getString(R.string.total_tasks, totalTasks));

        // Show/hide empty state with animation
        if (totalTasks == 0) {
            if (emptyStateView.getVisibility() != View.VISIBLE) {
                Animation fadeIn = AnimationUtils.loadAnimation(this, R.anim.fade_in);
                emptyStateView.startAnimation(fadeIn);
                emptyStateView.setVisibility(View.VISIBLE);
                swipeRefreshLayout.setVisibility(View.GONE);
            }
        } else {
            if (swipeRefreshLayout.getVisibility() != View.VISIBLE) {
                Animation fadeIn = AnimationUtils.loadAnimation(this, R.anim.fade_in);
                swipeRefreshLayout.startAnimation(fadeIn);
                emptyStateView.setVisibility(View.GONE);
                swipeRefreshLayout.setVisibility(View.VISIBLE);
            }
        }
    }

    private void refreshTasks() {
        // Simulate refresh delay
        new android.os.Handler().postDelayed(() -> {
            // Reload tasks from storage
            loadTasks();
            adapter.notifyDataSetChanged();
            updateTasksCount();
            applyFilters();
            swipeRefreshLayout.setRefreshing(false);
            Toast.makeText(this, getString(R.string.refresh_tasks), Toast.LENGTH_SHORT).show();
        }, 1000);
    }

    private void saveTasks() {
        SharedPreferences sharedPreferences = getSharedPreferences("MyTasks", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(tasks);
        editor.putString("task_list", json);
        editor.apply();
    }

    private void loadTasks() {
        SharedPreferences sharedPreferences = getSharedPreferences("MyTasks", Context.MODE_PRIVATE);
        Gson gson = new Gson();
        String json = sharedPreferences.getString("task_list", null);
        Type type = new TypeToken<ArrayList<Task>>() {}.getType();
        tasks = gson.fromJson(json, type);

        if (tasks == null) {
            tasks = new ArrayList<>();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_statistics) {
            Intent intent = new Intent(this, StatisticsActivity.class);
            startActivity(intent);
            return true;
        } else if (id == R.id.action_clear_all) {
            new AlertDialog.Builder(this)
                    .setTitle(getString(R.string.clear_all_tasks))
                    .setMessage(getString(R.string.clear_all_confirmation))
                    .setPositiveButton(getString(R.string.yes), (dialog, which) -> {
                        tasks.clear();
                        adapter.notifyDataSetChanged();
                        saveTasks();
                        updateTasksCount();
                        Toast.makeText(MainActivity.this, getString(R.string.all_tasks_cleared), Toast.LENGTH_SHORT).show();
                    })
                    .setNegativeButton(getString(R.string.no), null)
                    .show();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void setupNotifications() {
        NotificationHelper.createNotificationChannel(this);
    }

    private void checkOverdueTasks() {
        int notificationId = 1000;
        for (Task task : tasks) {
            if (task.isOverdue()) {
                NotificationHelper.showOverdueTaskNotification(this, task, notificationId++);
            } else if (task.isDueSoon()) {
                NotificationHelper.showTaskReminderNotification(this, task, notificationId++);
            }
        }
    }
}
