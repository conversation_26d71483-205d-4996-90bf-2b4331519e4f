package com.example.todolist;

import android.app.AlertDialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;
import java.util.ArrayList;
import java.util.Arrays;

import com.google.firebase.crashlytics.buildtools.reloc.com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.android.material.textfield.TextInputEditText;

import java.lang.reflect.Type;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

public class MainActivity extends AppCompatActivity {

    private Button addTaskButton;
    private Button filterButton;
    private ListView tasksListView;
    private TextView tasksCountTextView;
    private ArrayList<Task> tasks;
    private TaskAdapter adapter;
    private String currentFilter = "All";
    private String currentSearchText = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        initializeViews();
        setupTasksList();
        setupClickListeners();
    }

    private void initializeViews() {
        addTaskButton = findViewById(R.id.addTaskButton);
        filterButton = findViewById(R.id.filterButton);
        tasksListView = findViewById(R.id.tasksListView);
        tasksCountTextView = findViewById(R.id.tasksCountTextView);
    }

    private void setupTasksList() {
        tasks = new ArrayList<>();
        loadTasks();
        adapter = new TaskAdapter(this, tasks);
        tasksListView.setAdapter(adapter);
        updateTasksCount();
    }

    private void setupClickListeners() {
        // Add task button click listener
        addTaskButton.setOnClickListener(v -> showAddTaskDialog());

        // Filter button click listener
        filterButton.setOnClickListener(v -> showFilterDialog());

        // Task item click listener for editing
        tasksListView.setOnItemClickListener((parent, view, position, id) -> {
            Task task = adapter.getTaskAt(position);
            if (task != null) {
                showEditTaskDialog(task);
            }
        });

        // Task item long click listener for deleting
        tasksListView.setOnItemLongClickListener((parent, view, position, id) -> {
            Task task = adapter.getTaskAt(position);
            if (task != null) {
                showDeleteTaskDialog(task);
            }
            return true;
        });
    }

    private void showAddTaskDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_add_task, null);
        builder.setView(dialogView);

        TextInputEditText titleEditText = dialogView.findViewById(R.id.editTextTitle);
        TextInputEditText descriptionEditText = dialogView.findViewById(R.id.editTextDescription);
        Spinner statusSpinner = dialogView.findViewById(R.id.spinnerStatus);
        Button addButton = dialogView.findViewById(R.id.buttonAdd);
        Button cancelButton = dialogView.findViewById(R.id.buttonCancel);

        // Setup status spinner
        String[] statusOptions = {Task.STATUS_PENDING, Task.STATUS_IN_PROGRESS, Task.STATUS_COMPLETED};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, statusOptions);
        statusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(statusAdapter);

        AlertDialog dialog = builder.create();

        addButton.setOnClickListener(v -> {
            String title = titleEditText.getText().toString().trim();
            String description = descriptionEditText.getText().toString().trim();
            String status = statusSpinner.getSelectedItem().toString();

            if (title.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_title), Toast.LENGTH_SHORT).show();
                return;
            }

            if (description.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_description), Toast.LENGTH_SHORT).show();
                return;
            }

            Task newTask = new Task(title, description, status);
            tasks.add(newTask);
            adapter.notifyDataSetChanged();
            saveTasks();
            updateTasksCount();
            Toast.makeText(this, "Task added!", Toast.LENGTH_SHORT).show();
            dialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> dialog.dismiss());
        dialog.show();
    }

    private void showEditTaskDialog(Task task) {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_add_task, null);
        builder.setView(dialogView);

        TextInputEditText titleEditText = dialogView.findViewById(R.id.editTextTitle);
        TextInputEditText descriptionEditText = dialogView.findViewById(R.id.editTextDescription);
        Spinner statusSpinner = dialogView.findViewById(R.id.spinnerStatus);
        Button addButton = dialogView.findViewById(R.id.buttonAdd);
        Button cancelButton = dialogView.findViewById(R.id.buttonCancel);

        // Pre-fill with existing task data
        titleEditText.setText(task.getTitle());
        descriptionEditText.setText(task.getDescription());

        // Setup status spinner
        String[] statusOptions = {Task.STATUS_PENDING, Task.STATUS_IN_PROGRESS, Task.STATUS_COMPLETED};
        ArrayAdapter<String> statusAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, statusOptions);
        statusAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(statusAdapter);

        // Set current status
        int statusPosition = Arrays.asList(statusOptions).indexOf(task.getStatus());
        if (statusPosition >= 0) {
            statusSpinner.setSelection(statusPosition);
        }

        addButton.setText(getString(R.string.edit_task));
        AlertDialog dialog = builder.create();

        addButton.setOnClickListener(v -> {
            String title = titleEditText.getText().toString().trim();
            String description = descriptionEditText.getText().toString().trim();
            String status = statusSpinner.getSelectedItem().toString();

            if (title.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_title), Toast.LENGTH_SHORT).show();
                return;
            }

            if (description.isEmpty()) {
                Toast.makeText(this, getString(R.string.error_empty_description), Toast.LENGTH_SHORT).show();
                return;
            }

            task.setTitle(title);
            task.setDescription(description);
            task.setStatus(status);
            adapter.notifyDataSetChanged();
            saveTasks();
            Toast.makeText(this, getString(R.string.task_updated), Toast.LENGTH_SHORT).show();
            dialog.dismiss();
        });

        cancelButton.setOnClickListener(v -> dialog.dismiss());
        dialog.show();
    }

    private void showDeleteTaskDialog(Task task) {
        new AlertDialog.Builder(this)
                .setTitle(getString(R.string.delete_task))
                .setMessage("Are you sure you want to delete this task?")
                .setPositiveButton("Yes", (dialog, which) -> {
                    int originalPosition = adapter.getOriginalPosition(task);
                    if (originalPosition >= 0) {
                        tasks.remove(originalPosition);
                        adapter.notifyDataSetChanged();
                        saveTasks();
                        updateTasksCount();
                        Toast.makeText(this, getString(R.string.task_deleted), Toast.LENGTH_SHORT).show();
                    }
                })
                .setNegativeButton("No", null)
                .show();
    }

    private void showFilterDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_filter, null);
        builder.setView(dialogView);

        Spinner statusSpinner = dialogView.findViewById(R.id.spinnerFilterStatus);
        TextInputEditText searchEditText = dialogView.findViewById(R.id.editTextSearch);
        Button applyButton = dialogView.findViewById(R.id.buttonApplyFilter);
        Button clearButton = dialogView.findViewById(R.id.buttonClearFilter);

        // Setup status spinner
        String[] filterOptions = {"All", Task.STATUS_PENDING, Task.STATUS_IN_PROGRESS, Task.STATUS_COMPLETED};
        ArrayAdapter<String> filterAdapter = new ArrayAdapter<>(this, android.R.layout.simple_spinner_item, filterOptions);
        filterAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        statusSpinner.setAdapter(filterAdapter);

        // Set current filter
        int filterPosition = Arrays.asList(filterOptions).indexOf(currentFilter);
        if (filterPosition >= 0) {
            statusSpinner.setSelection(filterPosition);
        }

        // Set current search text
        searchEditText.setText(currentSearchText);

        AlertDialog dialog = builder.create();

        applyButton.setOnClickListener(v -> {
            currentFilter = statusSpinner.getSelectedItem().toString();
            currentSearchText = searchEditText.getText().toString().trim();
            applyFilters();
            dialog.dismiss();
        });

        clearButton.setOnClickListener(v -> {
            currentFilter = "All";
            currentSearchText = "";
            adapter.resetFilter();
            updateTasksCount();
            dialog.dismiss();
        });

        dialog.show();
    }

    private void applyFilters() {
        if (!currentSearchText.isEmpty()) {
            adapter.filterByTitle(currentSearchText);
        } else if (!currentFilter.equals("All")) {
            adapter.filterByStatus(currentFilter);
        } else {
            adapter.resetFilter();
        }
        updateTasksCount();
    }

    private void updateTasksCount() {
        int totalTasks = tasks.size();
        tasksCountTextView.setText(getString(R.string.total_tasks, totalTasks));
    }

    private void saveTasks() {
        SharedPreferences sharedPreferences = getSharedPreferences("MyTasks", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        Gson gson = new Gson();
        String json = gson.toJson(tasks);
        editor.putString("task_list", json);
        editor.apply();
    }

    private void loadTasks() {
        SharedPreferences sharedPreferences = getSharedPreferences("MyTasks", Context.MODE_PRIVATE);
        Gson gson = new Gson();
        String json = sharedPreferences.getString("task_list", null);
        Type type = new TypeToken<ArrayList<Task>>() {}.getType();
        tasks = gson.fromJson(json, type);

        if (tasks == null) {
            tasks = new ArrayList<>();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        if (id == R.id.action_clear_all) {
            new AlertDialog.Builder(this)
                    .setTitle("Clear All Tasks")
                    .setMessage("Are you sure you want to clear all tasks?")
                    .setPositiveButton("Yes", (dialog, which) -> {
                        tasks.clear();
                        adapter.notifyDataSetChanged();
                        saveTasks();
                        updateTasksCount();
                        Toast.makeText(MainActivity.this, "All tasks cleared!", Toast.LENGTH_SHORT).show();
                    })
                    .setNegativeButton("No", null)
                    .show();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
