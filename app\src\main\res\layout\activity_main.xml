<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/surface_color"
    tools:context=".MainActivity">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <!-- Header Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@color/primary_color"
        android:padding="16dp"
        android:elevation="4dp">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/app_name"
            android:textSize="24sp"
            android:textStyle="bold"
            android:textColor="@android:color/white"
            android:gravity="center"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingEnd="16dp">

            <Button
                android:id="@+id/filterButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/filter"
                android:backgroundTint="@color/secondary_color"
                android:textColor="@android:color/white"
                android:layout_marginStart="auto" />

        </LinearLayout>

    </LinearLayout>

    <!-- Tasks List Section -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:padding="8dp">

        <TextView
            android:id="@+id/tasksCountTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/total_tasks"
            android:textSize="14sp"
            android:textColor="@color/text_secondary"
            android:layout_marginBottom="8dp"
            android:padding="8dp" />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipeRefreshLayout"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ListView
                    android:id="@+id/tasksListView"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:divider="@android:color/transparent"
                    android:dividerHeight="4dp"
                    android:scrollbars="vertical"
                    android:background="@color/surface_color" />

            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

            <include
                android:id="@+id/emptyStateView"
                layout="@layout/empty_state"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </FrameLayout>

    </LinearLayout>

</LinearLayout>

<!-- Floating Action Button -->
<com.google.android.material.floatingactionbutton.FloatingActionButton
    android:id="@+id/fabAddTask"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|end"
    android:layout_margin="16dp"
    android:src="@drawable/ic_add"
    android:contentDescription="@string/add_task_button"
    app:backgroundTint="@color/accent_color"
    app:tint="@android:color/white"
    app:elevation="6dp"
    app:pressedTranslationZ="12dp" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
